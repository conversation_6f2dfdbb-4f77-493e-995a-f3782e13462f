import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { Collection, mongo, Types } from 'mongoose';

import { externalMigration } from '../helpers/merge-data';
import { omitNull } from '../helpers/transform.helper';
import { MigrationContext } from '../migration.service';
const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        _id: { $gt: nextId },
        'equipments.0': {
          $exists: true,
        },
        plannedDate: {
          $exists: true,
        },
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'jobequipments',
        localField: '_id',
        foreignField: 'job',
        as: 'jobequipments',
      },
    },
    {
      $match: {
        'jobequipments.0': {
          $exists: false,
        },
      },
    },
  ]);
};

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(async (job) =>
      omitNull({
        _id: job._id,
        subDocs: job.equipments.map((equipment: any) =>
          omitNull({
            _id: new ObjectId(),
            job: job._id,
            equipment: equipment._id,
            plannedDate: dayjs(job.plannedDate).utc().startOf('day'),
          }),
        ),
      }),
    ),
  );
};
const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await externalMigration({
      context,
      sourceCollectionName: 'jobs',
      destinationCollectionName: 'jobequipments',
      pagingFunc,
      tranformDataFunc: transformData,
    });

    const after = new Date().getTime();
    console.log(
      `#endregion migrate plannedDate of table JobEmployee with: ${after - before} ms`,
    );
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
