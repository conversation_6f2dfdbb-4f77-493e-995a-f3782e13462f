import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import dayjs from 'dayjs';
import _ from 'lodash';
import { Types } from 'mongoose';

import { buildFindAvailableEquipmentsAggregation } from '~/modules/equipment/equipment.helper';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { JobStatusEnum } from '~/shared/enums/job.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { EQUIPMENT_MESSAGE_KEYS } from '~/shared/message-keys/equipment.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, parseObjectId } from '~/utils';

import { JobModel } from '../job/job.model';
import { TaskModel } from '../task/task.model';
import {
  AvailableEquipmentQueryParamsDto,
  CreateEquipmentDto,
  EquipmentQueryParamsDto,
  UpdateEquipmentDto,
} from './equipment.dto';
import { EquipmentModel } from './equipment.model';

@Injectable()
export class EquipmentService {
  constructor(
    @InjectModel(EquipmentModel)
    private equipmentModel: MongooseModel<EquipmentModel>,
    @InjectModel(TaskModel)
    private taskModel: MongooseModel<TaskModel>,
    @InjectModel(JobModel)
    private jobModel: MongooseModel<JobModel>,
  ) {}

  async createEquipment(createEquipmentDto: CreateEquipmentDto) {
    const existedEquipment = await this.checkIsExistedInSystem(
      createEquipmentDto.description,
      createEquipmentDto.type,
      null,
    );

    if (existedEquipment)
      throw new BadRequestException(EQUIPMENT_MESSAGE_KEYS.EXISTS);

    return this.equipmentModel.create(createEquipmentDto);
  }

  async updateEquipment(id: string, updateEquipmentDto: UpdateEquipmentDto) {
    const equipmentId = parseObjectId(id);
    const equipment = await this.equipmentModel.findById(equipmentId).lean();

    if (!equipment) {
      throw new NotFoundException(EQUIPMENT_MESSAGE_KEYS.NOT_FOUND);
    }

    const isExistedEquipment = await this.checkIsExistedInSystem(
      updateEquipmentDto.description,
      equipment.type,
      equipmentId,
    );

    if (isExistedEquipment) {
      throw new BadRequestException(EQUIPMENT_MESSAGE_KEYS.EXISTS);
    }

    return this.equipmentModel
      .findOneAndUpdate(
        {
          _id: equipmentId,
          isDeleted: false,
        },
        {
          $set: {
            description: updateEquipmentDto.description,
            isActive: updateEquipmentDto.isActive,
          },
        },
        {
          new: true,
        },
      )
      .lean();
  }

  async getListEquipment(payload: EquipmentQueryParamsDto) {
    const { query, options } = buildQuery(payload, ['description']);
    let limit = options.limit;
    let offset = options.offset;
    if (payload.pageSize === -1) {
      limit = await this.equipmentModel.countDocuments(query).lean();
      offset = 0;
    }
    return await this.equipmentModel.paginate(
      {
        $and: [
          query,
          {
            $or: [{ isDeleted: false }, { isDeleted: { $exists: false } }],
          },
        ],
      },
      {
        limit: limit,
        offset: offset,
        sort: options.sort,
        select: '_id description type isActive',
        collation: options.collation,
      },
    );
  }

  /**
   *
   * FIXME: Might produce OOM if the amount of tasks/equipments & jobs in a particular period is too large
   *
   * @param payload Payload to get available equipments
   * @returns Available equipments
   */
  async getAvailableEquipments(payload: any) {
    const jobId = payload.jobId;
    const taskId = payload.taskId;
    const type = payload.equipmentType;
    const timezone = payload.timezone ?? '+00:00';
    const startDate = payload.startDate ?? payload.plannedDate;
    const endDate = payload.endDate ?? payload.plannedDate;

    const jobPayload = { jobId, type, startDate, endDate, timezone };
    const taskPayload = { taskId, type, startDate, endDate, timezone };

    const [jobEquipments, taskEquipments] = await Promise.all([
      this.getAvailableEquipmentsForJob(jobPayload),
      this.getAvailableEquipmentsForTask(taskPayload),
    ]);

    return _.intersectionWith(jobEquipments, taskEquipments, (j, t) =>
      j._id.equals(t._id),
    );
  }

  async getAvailableEquipmentsForJob(payload: any) {
    const { startDate, endDate, jobId, type, timezone } = payload;

    const queryJobs = {
      status: {
        $in: [
          JobStatusEnum.OPEN,
          JobStatusEnum.IN_PROGRESS,
          JobStatusEnum.READY,
        ],
      },
      isDeleted: false,
    } as any;

    if (jobId) {
      queryJobs._id = {
        $ne: jobId,
      };
    }

    const equipments = await this.equipmentModel
      .find({
        type: type ? type : { $exists: true },
        isActive: true,
      })
      .lean();

    const startDatePayload = dayjs(startDate)
      .utcOffset(timezone)
      .startOf('day')
      .toDate();
    const endDatePayload = dayjs(endDate)
      .utcOffset(timezone)
      .endOf('day')
      .toDate();

    const jobs = await this.jobModel
      .aggregate([
        {
          $match: queryJobs,
        },
        {
          $lookup: {
            from: 'jobequipments',
            localField: '_id',
            foreignField: 'job',
            as: 'jobEquipments',
            pipeline: [
              {
                $match: {
                  plannedDate: {
                    $gte: startDatePayload,
                    $lte: endDatePayload,
                  },
                },
              },
              {
                $lookup: {
                  from: 'equipment',
                  localField: 'equipment',
                  foreignField: '_id',
                  as: 'equipment',
                  pipeline: [
                    {
                      $match: {
                        isActive: true,
                        type: type ? { $eq: type } : { $exists: true },
                      },
                    },
                  ],
                },
              },
              {
                $set: {
                  equipment: {
                    $arrayElemAt: ['$equipment', 0],
                  },
                },
              },
              {
                $match: {
                  equipment: { $ne: null },
                },
              },
            ],
          },
        },
        {
          $match: {
            'jobEquipments.0': { $exists: true },
          },
        },
      ])
      .exec();

    const bookedEquipmentsByJob = _(jobs)
      .map((job) => job.jobEquipments.map((je) => je.equipment))
      .flatten()
      .value();

    const bookedEquipments = _.unionBy([...bookedEquipmentsByJob], (equip) =>
      equip._id.toString(),
    );

    return _.differenceBy(equipments, bookedEquipments, (equip) =>
      equip._id.toString(),
    );
  }

  async getAvailableEquipmentsForTask(payload: any) {
    const { startDate, endDate, taskId, type, timezone } = payload;

    const queryTasks = {
      startDate: {
        $lte: dayjs(endDate).utcOffset(timezone).endOf('day').toDate(),
      },
      endDate: {
        $gte: dayjs(startDate).utcOffset(timezone).startOf('day').toDate(),
      },
      isDeleted: false,
    } as any;

    if (taskId) {
      queryTasks._id = { $ne: taskId };
    }

    const equipments = await this.equipmentModel
      .find({
        type: type ? type : { $exists: true },
        isActive: true,
        $or: [{ isDeleted: false }, { isDeleted: { $exists: false } }],
      })
      .lean();

    const tasks = await this.taskModel
      .find(queryTasks)
      .populate('cars devices')
      .lean();

    const bookedEquipments = tasks.reduce((acc: any, task) => {
      const taskEquipments: any = [
        ...(task.cars || []),
        ...(task.devices || []),
      ];

      taskEquipments.forEach((equipment: any) => {
        if (!acc.some((e: any) => e._id === equipment._id)) {
          acc.push(equipment);
        }
      });
      return acc;
    }, []) as any;

    return equipments.filter((equipment) => {
      return !bookedEquipments.some((booked: any) =>
        booked._id.equals(equipment._id),
      );
    });
  }

  private async checkIsExistedInSystem(
    description: string,
    type: string,
    id?: Types.ObjectId | null,
  ): Promise<boolean> {
    const existedEquiment = await this.equipmentModel
      .findOne({
        type: type,
        $expr: {
          $eq: [{ $toLower: '$description' }, description.toLowerCase()],
        },
        _id: {
          $ne: id,
        },
      })
      .lean();

    return !!existedEquiment;
  }

  public async existsActiveOrThrow(ids: string[], type: EquipmentEnum) {
    const existedEquipments = await this.equipmentModel.find({
      _id: ids.map((id) => new Types.ObjectId(id)),
      isActive: true,
      type,
    });

    if (existedEquipments.length !== ids.length) {
      throw new NotFoundException(EQUIPMENT_MESSAGE_KEYS.NOT_FOUND);
    }
  }

  public async findAvailableEquipments(
    query: AvailableEquipmentQueryParamsDto,
  ) {
    const { stages, options } = buildFindAvailableEquipmentsAggregation(query);
    const aggregate = this.equipmentModel.aggregate(stages);
    return this.equipmentModel.aggregatePaginate(aggregate, options);
  }
}
