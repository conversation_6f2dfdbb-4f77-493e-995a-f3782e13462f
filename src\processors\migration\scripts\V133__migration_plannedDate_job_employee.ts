import dayjs from 'dayjs';
import { Collection, mongo, Types } from 'mongoose';

import { externalMigration } from '../helpers/merge-data';
import { omitNull } from '../helpers/transform.helper';
import { MigrationContext } from '../migration.service';
const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        _id: { $gt: nextId },
        plannedDate: { $exists: false },
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'jobs',
        localField: 'job',
        foreignField: '_id',
        as: 'job',
        pipeline: [
          {
            $project: {
              _id: 1,
              identifier: 1,
              title: 1,
              plannedDate: 1,
            },
          },
        ],
      },
    },
    {
      $match: {
        'job.0': {
          $exists: true,
        },
      },
    },
    {
      $set: {
        job: {
          $arrayElemAt: ['$job', 0],
        },
      },
    },
  ]);
};

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(async (jobEmployee: any) =>
      omitNull({
        _id: jobEmployee._id,
        job: jobEmployee.job._id,
        employee: jobEmployee.employee,
        plannedDate: dayjs(jobEmployee.job.plannedDate).utc().startOf('day'),
        actualHours: jobEmployee.actualHours,
        estimatedHours: jobEmployee.estimatedHours,
      }),
    ),
  );
};
const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await externalMigration({
      context,
      sourceCollectionName: 'jobemployees',
      destinationCollectionName: 'jobemployees',
      pagingFunc,
      tranformDataFunc: transformData,
    });

    const after = new Date().getTime();
    console.log(
      `#endregion migrate plannedDate of table JobEmployee with: ${after - before} ms`,
    );
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
