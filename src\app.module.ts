import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';

import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import appConfigs from './configs/app.config';
import databaseConfigs from './configs/database.config';
import emailConfig from './configs/email.config';
import microsoftTeamConfigs from './configs/microsoft-team.config';
import migrationConfigs from './configs/migration.config';
import nightRegistrationConfigs from './configs/night-registration.config';
import pdfConfigs from './configs/pdf.config';
import { AddressModule } from './modules/address/address.module';
import { AgreementlineModule } from './modules/agreementline/agreementline.module';
import { ArticleTemplateModule } from './modules/article-template/article-template.module';
import { BvCompanyModule } from './modules/bvcompany/bvcompany.module';
import { ContactModule } from './modules/contact/contact.module';
import { ContractModule } from './modules/contract/contract.module';
import { ContractTypeModule } from './modules/contract-type/contract-type.module';
import { CostcenterModule } from './modules/costcenter/costcenter.module';
import { CostlineModule } from './modules/costline/costline.module';
import { CostlinegeneralModule } from './modules/costlinegeneral/costlinegeneral.module';
import { CosttypeModule } from './modules/costtype/costtype.module';
import { CountryModule } from './modules/country/country.module';
import { DocumentFileModule } from './modules/document-file/document-file.module';
import { EmailTemplateModule } from './modules/email-template/email-template.module';
import { EquipmentModule } from './modules/equipment/equipment.module';
import { IdentifierModule } from './modules/identifier/identifier.module';
import { InvoiceModule } from './modules/invoice/invoice.module';
import { JobModule } from './modules/job/job.module';
import { JobPointModule } from './modules/job-point/job-point.module';
import { JobTemplateModule } from './modules/job-template/job-template.module';
import { LocationModule } from './modules/location/location.module';
import { LocationAdditionalModule } from './modules/location-addtional/location-additional.module';
import { LocationFileModule } from './modules/location-file/location-file.module';
import { NightRegistrationModule } from './modules/night-registration/night-registration.module';
import { PlanningModule } from './modules/planning/planning.module';
import { RegionModule } from './modules/region/region.module';
import { ReportLocationAdditionalModule } from './modules/report/location-additional/report-location-additional.module';
import { ReportModule } from './modules/report/report.module';
import { StatsOccupantModule } from './modules/stats-occupant/stats-occupant.module';
import { SyncHistoryModule } from './modules/sync-history/sync-history.module';
import { TaskModule } from './modules/task/task.module';
import { TeamModule } from './modules/team/team.module';
import { TenantModule } from './modules/tenant/tenant.module';
import { TenantRoleModule } from './modules/tenant-role/tenant-role.module';
import { TenantUserModule } from './modules/tenant-user/tenant-user.module';
import { UnitModule } from './modules/unit/unit.module';
import { DatabaseModule } from './processors/database/database.module';
import { EmailModule } from './processors/email/email.module';
import { CoreEventEmitterModule } from './processors/event-emitter/event-emitter.module';
import { GoogleMapModule } from './processors/google-map/google-map.module';
import { HealthzController } from './processors/health/healthz.controller';
import { LoggerModule } from './processors/logger/logger.module';
import { MigrationModule } from './processors/migration/migration.module';
import { ScheduleModule } from './processors/schedule/schedule.module';
import { SendAlertModule } from './processors/send-alert/send-alert.module';
import { TcpClientsModule } from './processors/tcp-client/tcp-client.module';
import { ThirdPartyConnectorModule } from './processors/third-party-connector/third-party-connector.module';

@Module({
  controllers: [HealthzController],
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        appConfigs,
        databaseConfigs,
        migrationConfigs,
        emailConfig,
        pdfConfigs,
        nightRegistrationConfigs,
        microsoftTeamConfigs,
      ],
    }),
    LoggerModule,
    DatabaseModule,
    TenantModule,
    TenantUserModule,
    TenantRoleModule,
    ContactModule,
    AddressModule,
    RegionModule,
    CountryModule,
    EquipmentModule,
    TeamModule,
    LocationModule,
    UnitModule,
    CostcenterModule,
    GoogleMapModule,
    DocumentFileModule,
    BvCompanyModule,
    MigrationModule,
    JobModule,
    JobPointModule,
    JobTemplateModule,
    IdentifierModule,
    CosttypeModule,
    ContractModule,
    AgreementlineModule,
    CostlinegeneralModule,
    CostlineModule,
    InvoiceModule,
    EmailModule,
    TcpClientsModule,
    EmailTemplateModule,
    ScheduleModule,
    ContractTypeModule,
    HttpModule,
    ThirdPartyConnectorModule,
    LocationAdditionalModule,
    NightRegistrationModule,
    SyncHistoryModule,
    LocationFileModule,
    TaskModule,
    PlanningModule,
    ReportLocationAdditionalModule,
    StatsOccupantModule,
    ReportModule,
    CoreEventEmitterModule,
    SendAlertModule,
    ArticleTemplateModule,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}
