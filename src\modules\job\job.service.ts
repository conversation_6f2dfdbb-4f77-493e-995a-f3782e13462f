import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Ref } from '@typegoose/typegoose';
import dayjs, { ManipulateType } from 'dayjs';
import * as https from 'https';
import {
  difference,
  differenceBy,
  flatMap,
  flatten,
  groupBy,
  has,
  isArray,
  map,
  omit,
  template,
  uniq,
  uniqBy,
} from 'lodash';
import { ObjectId } from 'mongodb';
import mongoose, { Model, Types } from 'mongoose';
import { PipelineStage } from 'mongoose';
import pLimit from 'p-limit';
import { firstValueFrom } from 'rxjs';

import {
  DATE_FORMAT_SLASH,
  INVENTORY_SERVICE_CLIENT,
  PDF_SERVICE_CLIENT,
  X_TENANT_ID_HEADER,
} from '~/constants/app.constant';
import { IdentifierService } from '~/modules/identifier/identifier.service';
import { EmailService } from '~/processors/email/email.service';
import { ContactRole } from '~/shared/enums/contact.enum';
import {
  AgreementLinePeriodType,
  ContractType,
} from '~/shared/enums/contract.enum';
import { CostTypeType } from '~/shared/enums/cost-type.enum';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { IdentifierType } from '~/shared/enums/identifier.enum';
import {
  GeneratePdfStatusEnum,
  JobFRuleEnum,
  JobPeriodTypeEnum,
  JobPointStatusEnum,
  JobReportTypeEnum,
  JobStatusEnum,
  JobStatusQueryEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';
import { ModuleNameEnum } from '~/shared/enums/module-name.enum';
import { TenantRoleEnum } from '~/shared/enums/tenant-role.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { JOB_KEYS } from '~/shared/message-keys/job.message-keys';
import { JOB_MESSAGES } from '~/shared/messages/job.message';
import { RESERVED_MESSAGES } from '~/shared/messages/reserved.message';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, formatNumberNL, parseObjectId } from '~/utils';
import { fillCSVContent } from '~/utils/csv.util';
import { isOddWeekOfYear, parseRangeDates } from '~/utils/date.util';
import { GroupJobPointsBySortedUnit } from '~/utils/jobPoint.util';
import { validteAndSortPosition } from '~/utils/position.util';

import { AddressModel } from '../address/address.model';
import { ContactModel } from '../contact/contact.model';
import { ContactService } from '../contact/contact.service';
import { CostLineModel } from '../costline/costline.model';
import { CostTypeModel } from '../costtype/costtype.model';
import { UploadFileModel } from '../document-file/upload-file.model';
import { EmailTemplateModel } from '../email-template/email-template.model';
import { EquipmentModel } from '../equipment/equipment.model';
import { EquipmentService } from '../equipment/equipment.service';
import { JobEmployeeModel } from '../job-employee/job-employee.model';
import { JobEquipmentModel } from '../job-equipment/job-equipment.model';
import { CreateJobPointDto } from '../job-point/dtos/job-point.dto';
import { JobPointAction, JobPointModel } from '../job-point/job-point.model';
import { LocationModel } from '../location/location.model';
import { NightRegistrationService } from '../night-registration/night-registration.service';
import { TenantService } from '../tenant/tenant.service';
import { TenantUserModel } from '../tenant-user/tenant-user.model';
import { TenantUserService } from '../tenant-user/tenant-user.service';
import { UnitModel } from '../unit/unit.model';
import { UnitService } from '../unit/unit.service';
import {
  CheckOverlapJobDto,
  CreateScheduleJobDto,
  DeleteJobDto,
  DeleteScheduleJobDto,
  GetPdfReportJobDto,
  JobQuerySchemaDto,
  MobileCoWorkerSyncDataOfJobDto,
  MobileJobQuerySchemaDto,
  UpdatePlanningJobDto,
  UpdateScheduleJobSettingsDto,
  ViewSummaryJobDto,
} from './dtos/job.dto';
import {
  aggregateOfViewSummaryOfJob,
  buildEmployeesSchedulesAggregate,
  buildEquipmentSchedulesAggregate,
  generateRangeDatesMatchConditions,
  getDateOfWeek,
  getDayInMonth,
  getJobStatus,
  getRangeOfDates,
  groupActions,
  mobileAggregateOfDetailJob,
  mobileGetAllJobsSelectedFields,
  MONTH_GENERATE,
  portalAggregateOfDetailJob,
  projectPipelineStage,
  projectPipelineStageMobile,
  removeUnitsDoesNotHavePoints,
  TypeRule,
  TypeRuleDayJS,
  validateImages,
  validatePeriodTypeOfJob,
  validateReadyJobPoints,
  validateUpdateData,
  validateUpdateJobFlow,
} from './job.helper';
import { JobDocument, JobModel } from './job.model';
@Injectable()
export class JobService {
  private readonly logger = new Logger(JobService.name);

  constructor(
    @Inject(INVENTORY_SERVICE_CLIENT)
    private readonly inventoryClient: ClientProxy,

    @Inject(PDF_SERVICE_CLIENT)
    private readonly pdfClient: ClientProxy,

    @InjectModel(ContactModel)
    private readonly contactModel: MongooseModel<ContactModel>,
    @InjectModel(LocationModel)
    private readonly locationModel: MongooseModel<LocationModel>,
    @InjectModel(TenantUserModel)
    private readonly tenantUserModel: MongooseModel<TenantUserModel>,
    @InjectModel(JobModel)
    private readonly jobModel: MongooseModel<JobModel>,
    @InjectModel(JobPointModel)
    private readonly jobPointModel: MongooseModel<JobPointModel>,
    @InjectModel(JobEmployeeModel)
    private readonly jobEmployeeModel: MongooseModel<JobEmployeeModel>,
    @InjectModel(EquipmentModel)
    private readonly equipmentModel: Model<EquipmentModel>,
    @InjectModel(UnitModel)
    private readonly unitModel: Model<UnitModel>,
    @InjectModel(UploadFileModel)
    private readonly uploadFileModel: Model<UploadFileModel>,
    @InjectModel(EmailTemplateModel)
    private readonly emailTemplateModel: Model<EmailTemplateModel>,
    @InjectModel(CostLineModel)
    private readonly costLineModel: Model<CostLineModel>,
    @InjectModel(CostTypeModel)
    private readonly costTypeModel: Model<CostTypeModel>,
    @InjectModel(JobEquipmentModel)
    private readonly jobEquipmentModel: Model<JobEquipmentModel>,

    private readonly unitService: UnitService,
    private readonly identifierService: IdentifierService,
    private readonly tenantUserService: TenantUserService,
    private readonly contactService: ContactService,
    private readonly equipmentService: EquipmentService,
    private readonly tenantService: TenantService,
    private readonly emailService: EmailService,
    private readonly nightRegistrationService: NightRegistrationService,

    private httpService: HttpService,
  ) {}

  //#region global function
  async findAll(payload: JobQuerySchemaDto) {
    if (payload.units) {
      payload.units = isArray(payload.units) ? payload.units : [payload.units];
    }

    const { user, ...rest } = payload;
    const queryOptions = buildQuery(rest, ['identifier', 'title']);

    const { options } = queryOptions;
    let { query } = queryOptions;

    if (query.schedulerId) {
      query.fIdentifier = { $regex: query.schedulerId, $options: 'i' };
      query = omit(query, ['schedulerId']);
    }

    let extendAggregate = [] as PipelineStage[];

    const allowedJobStatus = await this.getAllowedJobStatusForGetAllJob(
      user,
      rest.jobType,
    );

    if (query.startDate || query.endDate) {
      const { djsStartDate, djsEndDate } = parseRangeDates(
        query.startDate,
        query.endDate,
      );
      const matchConditions = generateRangeDatesMatchConditions(
        djsStartDate,
        djsEndDate,
      );

      extendAggregate = [
        ...extendAggregate,
        {
          $match: matchConditions,
        },
      ];

      query = omit(query, ['startDate', 'endDate']);
    }

    if (query.status) {
      extendAggregate = [
        ...extendAggregate,
        {
          $match: {
            type: { $ne: JobPeriodTypeEnum.PERIODIC },
          },
        },
      ];
    }

    if (query.team || query.location || query.assignee || query.planner) {
      extendAggregate = [
        ...extendAggregate,
        {
          $match: {
            $and: [
              ...(query.team
                ? [{ 'locationTeamInfo._id': query.team?.toString() }]
                : []),
              ...(query.location
                ? [{ 'locationInfo._id': query.location?.toString() }]
                : []),
              ...(query.assignee
                ? [{ 'assigneeInfo._id': query.assignee?.toString() }]
                : []),
              ...(query.planner
                ? [{ 'plannerInfo._id': query.planner?.toString() }]
                : []),
            ],
          },
        },
      ];
    }

    query.isDeleted = false;
    // isActive is always true
    query.isActive = true;

    const aggregate = this.jobModel.aggregate(
      [
        {
          $match: omit(query, ['team', 'platform']),
        },
        {
          $match: {
            status: {
              $in: allowedJobStatus,
            },
          },
        },
        ...extendAggregate,
        query.platform !== 'mb'
          ? projectPipelineStage
          : projectPipelineStageMobile,
        { $sort: options.sort },
      ],
      {
        collation: options.collation,
      },
    );

    return this.jobModel.aggregatePaginate(aggregate, {
      limit: options.limit,
      offset: options.offset,
      allowDiskUse: true,
    });
  }

  async findAllMobile(payload: MobileJobQuerySchemaDto) {
    const { platform, lastSyncedAt, user, isDeleted } = payload;
    if (platform !== 'mb') {
      throw new BadRequestException(JOB_KEYS.API_ONLY_SUPPORT_FOR_MOBILE);
    }

    let query: any = {};

    const jobTypes = [
      JobTypeEnum.INSPECTION,
      JobTypeEnum.MAINTENANCE,
      JobTypeEnum.CLEANING,
    ];

    await this.getJobTypesWhenGetAllJobForMobile(
      jobTypes,
      user,
      TenantRoleEnum.INSPECTOR,
      JobTypeEnum.INSPECTION,
    );

    await this.getJobTypesWhenGetAllJobForMobile(
      jobTypes,
      user,
      TenantRoleEnum.MECHANIC,
      JobTypeEnum.MAINTENANCE,
    );

    await this.getJobTypesWhenGetAllJobForMobile(
      jobTypes,
      user,
      TenantRoleEnum.CLEANER,
      JobTypeEnum.CLEANING,
    );

    if (lastSyncedAt) {
      query = {
        updatedAt: {
          $gte: dayjs(lastSyncedAt).toDate(),
        },
      };
    }

    // isActive is always true
    query.isActive = true;

    if (isDeleted) {
      query.isDeleted = isDeleted === 'true';
    }

    query.jobType = {
      $in: jobTypes,
    };

    const jobEmployees = await this.jobEmployeeModel.find({ employee: user });
    const jobIdSets = new Set(
      jobEmployees.map((jobEmployee) => jobEmployee.job),
    );
    const jobIds = Array.from(jobIdSets).map((jobIdSet) => jobIdSet);

    query._id = {
      $in: jobIds,
    };
    const limit = await this.jobModel.countDocuments(query).lean();

    const aggregates = this.jobModel.aggregate([
      {
        $match: {
          ...query,
          type: {
            $ne: JobPeriodTypeEnum.PERIODIC,
          },
        },
      },
      { $sort: { ['updatedAt']: -1 } },
      {
        $project: mobileGetAllJobsSelectedFields,
      },
    ]);

    return await this.jobModel.aggregatePaginate(aggregates, {
      limit: limit,
      offset: 0,
    });
  }

  async findOne(payload: any) {
    const { id, platform, user = null } = payload;
    const jobs = await this.jobModel
      .aggregate([
        {
          $match: {
            _id: new ObjectId(id as string),
            isDeleted: false,
          },
        },
        ...(platform === 'mb'
          ? mobileAggregateOfDetailJob
          : portalAggregateOfDetailJob),
      ])
      .exec();

    const job = jobs.length > 0 ? jobs[0] : null;

    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const statusNotAllowMobileGetDetailJob = [JobStatusEnum.READY];

    if (
      platform === 'mb' &&
      statusNotAllowMobileGetDetailJob.includes(job.status)
    ) {
      throw new BadRequestException(
        JOB_KEYS.JOB_STATUS_NOT_ALLOW_VIEW_DETAIL_JOB,
      );
    }

    const employees = job.employees || [];

    // return error if current user is not in employees list when using mobile platform
    if (
      platform === 'mb' &&
      user &&
      !employees.some((employee) => employee.employee._id.toString() === user)
    ) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const { locationInfo, assigneeInfo, plannerInfo, ...rest } = job;
    const locationData = platform === 'mb' ? job.location : locationInfo;
    return {
      ...rest,
      location: locationData,
      assignee: assigneeInfo,
      planner: plannerInfo,
      employees: employees,
    };
  }

  async findReviewDetail(payload: any) {
    const { id, platform, user } = payload;

    const job = await this.jobModel.findById(id).lean();

    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const statusNotAllowMobileGetReviewJob = [JobStatusEnum.READY];

    if (
      platform === 'mb' &&
      statusNotAllowMobileGetReviewJob.includes(job.status)
    ) {
      throw new BadRequestException(
        JOB_KEYS.JOB_STATUS_NOT_ALLOW_VIEW_REVIEW_JOB,
      );
    }

    // return error if current user is not in employees list when using mobile platform
    const employees = await this.jobEmployeeModel
      .find({ job: job._id })
      .populate([
        {
          path: 'employee',
          select: '_id displayName',
        },
      ])
      .select('employee estimatedHours actualHours')
      .lean();

    if (
      platform === 'mb' &&
      !employees.some((employee) => employee.employee._id.toString() === user)
    ) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const jobPoints = await this.jobPointModel
      .find({ job: id })
      .populate([
        {
          path: 'unit',
          select: '_id name parent position',
          populate: { path: 'parent', select: '_id name position' },
        },
        {
          path: 'costLines',
          select: '_id description position price quantity totalPrice',
          populate: { path: 'costType', select: '_id name itemCode' },
        },
      ])
      .select(
        '_id status description position images actions notes unit costLines',
      )
      .lean();

    const jobPointWithSortedCostLines = jobPoints.map((jobPoint) => {
      return {
        ...jobPoint,
        costLines:
          jobPoint.costLines && jobPoint.costLines.length > 0
            ? [...jobPoint.costLines].sort(
                (a: any, b: any) => a.position - b.position,
              )
            : [],
      };
    });

    const rootUnit = await this.unitService.findRootUnitOfLocation(
      job.location.toString(),
    );

    // Group job points by unit and sort by position within unit's parent and unit
    const jobPointsGroupByUnitSorted = GroupJobPointsBySortedUnit(
      jobPointWithSortedCostLines,
      rootUnit,
    );

    // TODO: Add residents check in / check out of job

    // TODO: Add information of inventory
    const mapedCheckInCheckOutUnits =
      await this.MapCheckInAndCheckOutToJobPoint(
        jobPointsGroupByUnitSorted,
        job._id,
      );
    return {
      units: mapedCheckInCheckOutUnits,
    };
  }

  async create(
    data: any & {
      fIdentifier?: string;
    },
  ) {
    const {
      units,
      user,
      employees = [],
      equipments = [],
      reserved,
      ...rest
    } = data;

    await this.validatePermissionCreateJob(user, rest.jobType, rest.platform);

    const unitsPayload = removeUnitsDoesNotHavePoints(units);
    const unitIds = unitsPayload.map((unit) => parseObjectId(unit._id));

    const identifier = await this.identifierService.generateIdentifier(
      IdentifierType.JOB,
    );

    if (units) {
      for (const unit of units) {
        validteAndSortPosition(unit.points, ModuleNameEnum.JOB, true);
      }
    }

    if (
      rest.reportType &&
      rest.reportType !== JobReportTypeEnum.INTERNAL &&
      !rest.reportContact
    ) {
      throw new BadRequestException(JOB_KEYS.REPORT_CONTACT_REQUIRED);
    }

    const employeeIds = employees.map((emp) => emp.employee);

    const location = await this.locationModel
      .findById(rest.location)
      .populate('team')
      .select('fullAddress team')
      .lean();

    if (!location) {
      throw new BadRequestException(JOB_KEYS.LOCATION_NOT_FOUND);
    }

    await this.validateUnitsOfPayload(rest.location, units);

    const uniqEmployeeIds = uniq(employeeIds);

    await this.validateEmployees(
      rest.assignee,
      uniqEmployeeIds as string[],
      rest.jobType,
    );

    if (rest.reportType) {
      rest.reportContact = await this.getValidReportContact(
        rest.reportType,
        rest.reportContact,
      );
    }

    const isMultipleDaysJob = rest.type === JobPeriodTypeEnum.MULTIPLE_DAYS;

    const equipmentIds = equipments.map((val) => val.equipment);
    const uniqEquipments = uniq(equipmentIds);
    await this.validateEquipments(uniqEquipments as string[]);

    validatePeriodTypeOfJob(
      rest.type as JobPeriodTypeEnum,
      rest.jobType as JobTypeEnum,
    );

    if (rest.images) {
      validateImages(rest.images);
    }
    const [assignee, planner, reportContact] = await Promise.all([
      this.tenantUserModel
        .findById(rest.assignee)
        .select('displayName firstName lastName email')
        .lean(),
      this.tenantUserModel
        .findById(user)
        .select('displayName firstName lastName email')
        .lean(),
      this.contactModel
        .findById(rest.reportContact)
        .select('displayName name email')
        .lean(),
    ]);

    const job = await this.jobModel.create({
      ...rest,
      planner: user,
      units: unitIds,
      identifier: identifier,
      locationTeamInfo: location?.team,
      locationInfo: {
        _id: location?._id,
        fullAddress: location?.fullAddress,
      },
      assigneeInfo: {
        _id: assignee?._id,
        displayName: assignee?.displayName,
        email: assignee?.email,
      },
      plannerInfo: {
        _id: planner?._id,
        displayName: planner?.displayName,

        email: planner?.email,
      },
      reportContactInfo: {
        _id: reportContact?._id,
        displayName: reportContact?.displayName,
        email: reportContact?.email,
      },
    });

    const jobId = job._id.toString();

    // Flatten the points
    const points = flatMap(unitsPayload, (unit) =>
      unit.points.map((point) => {
        return {
          ...point,
          unit: unit._id,
          job: jobId,
        };
      }),
    );

    await this.createJobPoints(points);

    if (rest.platform !== 'mb') {
      const jobEmployees = employees.map((employee) => {
        let plannedDate;
        if (isMultipleDaysJob) {
          plannedDate = dayjs(employee.plannedDate).utc().startOf('day');
        } else if (rest.plannedDate) {
          plannedDate = dayjs(rest.plannedDate).utc().startOf('day');
        } else {
          plannedDate = rest.plannedDate;
        }
        return {
          ...employee,
          job: job._id,
          plannedDate,
        };
      });

      this.jobEmployeeModel
        .insertMany(jobEmployees)
        .then(() => {
          this.logger.log(
            `Job employees of job ${job._id.toString()} inserted successfully`,
          );
        })
        .catch((error) => {
          this.logger.error(
            `Error inserting job employees of job ${job._id.toString()}`,
            error,
          );
        });

      const createJobEquipments = equipments.map((equipment) => {
        let plannedDate;
        if (isMultipleDaysJob) {
          plannedDate = dayjs(equipment.plannedDate).utc().startOf('day');
        } else if (rest.plannedDate) {
          plannedDate = dayjs(rest.plannedDate).utc().startOf('day');
        } else {
          plannedDate = rest.plannedDate;
        }
        return {
          equipment: equipment.equipment,
          job: job._id,
          plannedDate: plannedDate,
        };
      });
      this.jobEquipmentModel
        .insertMany(createJobEquipments)
        .then(() => {
          this.logger.log(
            `Job equipments of job ${job._id.toString()} inserted successfully`,
          );
        })
        .catch((error) => {
          this.logger.error(
            `Error inserting job equipments of job ${job._id.toString()}`,
            error,
          );
        });
    } else {
      const employee = {
        employee: rest.assignee,
        estimatedHours: rest.estimatedHours,
        job: job._id,
        plannedDate: dayjs(rest.plannedDate).utc().startOf('day'),
      };
      await this.jobEmployeeModel.create(employee);
    }

    // Create scheduled job if job is periodic
    if (rest.type === JobPeriodTypeEnum.PERIODIC) {
      const data = rest as CreateScheduleJobDto;

      const scheduleJobId = await this.identifierService.generateIdentifier(
        IdentifierType.SCHEDULER,
      );

      await this.createScheduleJob(jobId, {
        ...data,
        user,
        units,
        fIdentifier: scheduleJobId!,
      });
    }

    await this.identifierService.deleteIdentifier(
      IdentifierType.JOB,
      identifier,
    );

    if (reserved) {
      await firstValueFrom(
        this.inventoryClient.send(
          { cmd: RESERVED_MESSAGES.CREATE_RESERVED },
          {
            body: {
              ...reserved,
              job: jobId,
            },
            user,
          },
        ),
      );
    }

    this.sendEmailWhenAssigneeJob(jobId, data.tenantId)
      .then(() => {
        this.logger.log('Send email to assignee successfully');
      })
      .catch((error) => {
        this.logger.error('Send email to assignee failed with error', error);
      });

    return await this.findOne({
      id: job._id.toString(),
      platform: rest.platform,
    });
  }

  async updateJob(id: string, payload: any, headers: any) {
    const job = await this.jobModel.findById(id).lean();

    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const platform = payload.platform;

    // Prepare and validate all update data
    const {
      status,
      isOverdue,
      units,
      user,
      employees,
      reserved,
      isOverrideRegular,
      equipments,
      rest,
    } = await this.prepareJobUpdateData(job, payload);

    const currentEmployees = await this.jobEmployeeModel
      .find({ job: job._id })
      .populate([
        {
          path: 'employee',
          select: '_id displayName',
        },
      ])
      .select('employee estimatedHours actualHours')
      .lean();

    // return error if current user is not in employees list when using mobile platform
    if (
      platform === 'mb' &&
      !currentEmployees.some(
        (employee) => employee.employee._id.toString() === payload.user,
      )
    ) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    if (platform === 'mb') {
      await this.validateUserUpdateJob(user, job);
    }

    const [assignee, planner, reportContact] = await Promise.all([
      this.tenantUserModel
        .findById(rest.assignee ?? job.assignee)
        .select('displayName firstName lastName email')
        .lean(),
      this.tenantUserModel
        .findById(rest.planner ?? job.planner)
        .select('displayName firstName lastName email')
        .lean(),
      this.contactModel
        .findById(rest.reportContact ?? job.reportContact)
        .select('displayName name email')
        .lean(),
    ]);

    const updatedUnitIds = await this.processUnitChanges(job, units, platform);

    await this.deleteOldImages(job.images, rest.images, headers);

    await this.processReservations(reserved, job._id.toString(), payload.user);

    const currentAssignee = job.assignee;

    await this.jobModel.updateOne(
      { _id: job._id },
      {
        ...rest,
        units: updatedUnitIds.length > 0 ? updatedUnitIds : job.units,
        assigneeInfo: assignee,
        plannerInfo: planner,
        reportContactInfo: rest.reportContact ? reportContact : null,
        updatedBy: user,
        ...(isOverdue !== undefined ? { isOverdue } : ''),
      },
    );

    await this.processAssigneeChange(
      currentAssignee,
      rest.assignee,
      id,
      headers[X_TENANT_ID_HEADER],
    );

    await this.updateEmployeesAndEquipmentsOfJob(
      employees,
      equipments,
      job,
      status,
    );

    await this.processActualHoursUpdate(
      platform,
      rest.actualHours,
      job._id.toString(),
      user,
    );

    // Create scheduled job if job is periodic
    if (job.type === JobPeriodTypeEnum.PERIODIC) {
      await this.updateScheduleJob(
        job._id.toString(),
        isOverrideRegular,
        platform,
      );
    }

    return await this.findOne({ id, platform, user });
  }

  async deleteJobs(payload: DeleteJobDto) {
    const deletedJobs = await this.jobModel
      .find({
        _id: { $in: payload.jobIds },
        isDeleted: false,
      })
      .lean();

    if (!deletedJobs || deletedJobs.length != payload.jobIds.length) {
      throw new BadRequestException(JOB_KEYS.JOBS_NOT_FOUND);
    }

    if (deletedJobs.some((job) => !['open', 'closed'].includes(job.status))) {
      throw new BadRequestException(
        JOB_KEYS.ONLY_DELETE_JOB_THAT_HAVE_STATUS_OPEN_OR_CLOSED,
      );
    }

    const jobType = deletedJobs[0].jobType;

    const allowedDeletedStatus = await this.getAllowedJobStatusForDeleteJob(
      payload.user,
      jobType,
    );

    if (
      deletedJobs.some(
        (job) =>
          !allowedDeletedStatus.includes(
            job.status as unknown as JobStatusQueryEnum,
          ),
      )
    ) {
      throw new ForbiddenException();
    }

    const deletedJobIds = deletedJobs.map((job) => job._id);

    await Promise.all(
      deletedJobIds.map((id) => {
        return firstValueFrom(
          this.inventoryClient.send(
            {
              cmd: RESERVED_MESSAGES.DELETE_RESERVED,
            },
            {
              id: id.toString(),
              user: payload.user,
            },
          ),
        );
      }),
    );

    const currentISODate = dayjs().utc().toISOString();

    await this.jobEmployeeModel
      .updateMany(
        { job: { $in: deletedJobIds } },
        { $set: { isDeleted: true, deletedAt: currentISODate } },
      )
      .exec();

    await this.jobPointModel
      .updateMany(
        { job: { $in: deletedJobIds } },
        { $set: { isDeleted: true, deletedAt: currentISODate } },
      )
      .exec();

    return await this.jobModel
      .updateMany(
        { _id: { $in: deletedJobIds } },
        {
          $set: {
            isDeleted: true,
            updatedBy: payload.user,
            deletedAt: currentISODate,
          },
        },
      )
      .exec();
  }

  async updateStatus(data: any) {
    const { id, platform = 'portal', status, headers, ...rest } = data;

    let followUpJobs = [] as any[];

    const foundJob = await this.jobModel.findById(id).lean();

    if (!foundJob) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    validateUpdateJobFlow({
      currentStatus: foundJob.status,
      nextStatus: status,
      platform,
    });

    switch (status) {
      case JobStatusEnum.READY:
        if (!rest.inter)
          rest.units?.forEach((unit: any) => {
            validateReadyJobPoints(unit.points);
          });
        break;
      case JobStatusEnum.REJECT:
        await this.validateReviewer(rest.user, foundJob.jobType);
        break;
      case JobStatusEnum.COMPLETE:
        await this.validatePayloadDataWhenCompleteJob(
          data,
          foundJob.reportType,
        );
        break;
    }

    await this.updateJob(
      id,
      {
        ...rest,
        status,
        platform,
        ...(status === JobStatusEnum.REJECT && {
          instructions: `${foundJob.instructions}\n${
            rest.instructions ? `Reject reason: ${rest.instructions}` : ''
          }`,
          rejectedBy: rest.user,
          rejectedAt: dayjs().utc().toISOString(),
        }),
        ...(foundJob.status === JobStatusEnum.OPEN &&
          dayjs(foundJob.plannedDate).isBefore(
            dayjs().utc().startOf('day'),
          ) && {
            isOverdue: true,
          }),
      },
      headers,
    ).then(async (res) => {
      const nextStatus = getJobStatus(status);

      let isHasCostLine = false;
      if (nextStatus === 'completed' || nextStatus === 'closed') {
        isHasCostLine = await this.updateCostLineWhenCompleteJob(id);
        firstValueFrom(
          this.pdfClient.send(
            { cmd: JOB_MESSAGES.GENERATE_PDF_REPORT_OF_JOB },
            {
              id: id,
              headers,
            },
          ),
        )
          .then(async ({ pdfPublicUrl, residentPdfPublicUrl }) => {
            await this.jobModel.updateOne(
              { _id: foundJob._id },
              {
                pdfPublicUrl: pdfPublicUrl,
                generatePdfStatus: GeneratePdfStatusEnum.DONE,
              },
            );
            this.logger.log('Update pdfPublicUrl of job completed');

            await this.sendEmailWhenCompletingJob(
              id,
              headers[X_TENANT_ID_HEADER],
              pdfPublicUrl,
              residentPdfPublicUrl,
              data.isSendRC,
              data.isSendRR,
            );
            this.logger.log('Send email of job completed successfully');
          })
          .catch((error) => {
            console.error('Error during PDF generation', error);
          });
      }

      if (status === JobStatusEnum.REJECT) {
        await this.sendEmailWhenRejectingJob(
          id,
          headers[X_TENANT_ID_HEADER],
          rest.instructions,
        );
      }

      await this.jobModel.updateOne(
        {
          _id: id,
        },
        {
          status:
            nextStatus === 'completed'
              ? isHasCostLine
                ? 'completed'
                : 'closed'
              : nextStatus,
        },
      );

      if (status === JobStatusEnum.COMPLETE) {
        followUpJobs = await this.createFollowUpJobs(
          id,
          rest.user,
          headers[X_TENANT_ID_HEADER],
        );
      }

      // complete stock transaction when status is ready
      if (status === JobStatusEnum.READY && !has(foundJob, 'rejectedAt')) {
        await firstValueFrom(
          this.inventoryClient.send(
            { cmd: RESERVED_MESSAGES.COMPLETE_INSPECTION },
            {
              id: id.toString(),
              user: rest.user,
            },
          ),
        );
      }

      return res;
    });

    return {
      message: JOB_KEYS.UPDATE_STATUS_SUCCESS,
      ...(followUpJobs.length > 0 && { jobs: followUpJobs }),
    };
  }

  async createScheduleJob(
    jobId: string,
    data: CreateScheduleJobDto & {
      fIdentifier?: string;
      startGenerateDate?: string;
      endGenerateDate?: string;
    },
  ) {
    const scheduleJob = await this.jobModel
      .findOneAndUpdate(
        {
          _id: jobId,
        },
        {
          fIdentifier: data.fIdentifier,
        },
        {
          new: true,
        },
      )
      .lean();

    if (!scheduleJob) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const scheduleJobPoints = await this.getJobPoints(jobId);

    const scheduleEmployees = await this.getEmployees(jobId);

    const scheduleEquipments = await this.getJobEquipments(jobId);

    const {
      fInterval,
      fRule,
      fDays = [],
      fDayInMonth,
      fStartDate,
      fEndDate,
      startGenerateDate,
      endGenerateDate,
    } = data;

    const djsCurrentDate = dayjs().utc().startOf('day');

    const djsStartDate = dayjs(startGenerateDate || fStartDate)
      .utc()
      .startOf('day');

    const djsEndGenerateDate = endGenerateDate
      ? dayjs(endGenerateDate).utc()
      : dayjs(fStartDate)
          .utc()
          .add(MONTH_GENERATE, 'month')
          .endOf('month')
          .endOf('day');

    const djsEndDate = djsEndGenerateDate.isBefore(
      dayjs(fEndDate).utc().endOf('day'),
    )
      ? djsEndGenerateDate
      : dayjs(fEndDate).utc().endOf('day');

    this.logger.log(`Scheduler Identifier: ${data.fIdentifier}`);
    this.logger.log(
      `Scheduler Start Date: ${dayjs(fStartDate).utc().startOf('day').toISOString()}`,
    );
    this.logger.log(
      `Scheduler End Date: ${dayjs(fEndDate).utc().endOf('day').toISOString()}`,
    );
    !!startGenerateDate &&
      this.logger.log(`Start Generate Date: ${djsStartDate.toISOString()}`);
    !!endGenerateDate &&
      this.logger.log(`End Generate Date: ${djsEndGenerateDate.toISOString()}`);

    let typeRule = TypeRule.DAYS;
    let dateRange = [] as string[];

    switch (fRule) {
      case JobFRuleEnum.DAILY: {
        typeRule = TypeRule.DAYS;
        dateRange = getRangeOfDates({
          start: djsStartDate,
          end: djsEndGenerateDate,
          key: typeRule,
          offset: fInterval,
        });
        break;
      }
      case JobFRuleEnum.WEEKLY: {
        typeRule = TypeRule.WEEKS;
        dateRange = getRangeOfDates({
          start: djsStartDate,
          end: djsEndGenerateDate,
          key: typeRule,
          offset: fInterval,
        });

        dateRange = flatten(
          dateRange.map((date) => getDateOfWeek(date, fDays)),
        );
        break;
      }
      case JobFRuleEnum.MONTHLY: {
        typeRule = TypeRule.MONTHS;
        dateRange = getRangeOfDates({
          start: djsStartDate,
          end: djsEndGenerateDate,
          key: typeRule,
          offset: fInterval,
        });

        if (fDays?.length) {
          dateRange = dateRange.map((date) => {
            const listDayInMonth = getDayInMonth(date, fDays[0]);

            if (fDayInMonth === 5) {
              return listDayInMonth[listDayInMonth.length - 1];
            }

            return listDayInMonth[fDayInMonth! - 1];
          });
        }

        break;
      }
      case JobFRuleEnum.YEARLY: {
        typeRule = TypeRule.YEARS;
        dateRange = getRangeOfDates({
          start: djsStartDate,
          end: djsEndGenerateDate,
          key: typeRule,
          offset: fInterval,
        });
        break;
      }

      default:
    }

    dateRange = dateRange.filter(
      (date) =>
        dayjs(date).isSameOrAfter(
          djsCurrentDate.isSameOrAfter(djsStartDate)
            ? djsCurrentDate
            : djsStartDate,
        ) && dayjs(date).isSameOrBefore(djsEndDate),
    );

    const limit = pLimit(1);

    const createPromises = dateRange.map((date) =>
      limit(async () => {
        const newJobId = new mongoose.Types.ObjectId();

        const jobPoints = scheduleJobPoints.map(
          ({ _id, createdAt: _createdAt, updatedAt: _updatedAt, ...rest }) => {
            return {
              ...rest,
              job: newJobId,
            };
          },
          [],
        );

        const employees = scheduleEmployees.map((e) => {
          return {
            employee: e.employee,
            estimatedHours: e.estimatedHours,
            plannedDate: dayjs(date).utc().startOf('day'),
            job: newJobId,
          };
        });

        const equipments = scheduleEquipments.map((e) => {
          return {
            equipment: e.equipment,
            plannedDate: dayjs(date).utc().startOf('day'),
            job: newJobId,
          };
        });

        await this.jobPointModel.insertMany(jobPoints);
        await this.jobEmployeeModel.insertMany(employees);
        await this.jobEquipmentModel.insertMany(equipments);

        const identifier = await this.identifierService.generateIdentifier(
          IdentifierType.JOB,
        );

        return this.jobModel.create({
          ...omit(scheduleJob, ['_id', 'createdAt', 'updatedAt', 'identifier']),
          identifier,
          title: `${scheduleJob.title}`,
          type: JobPeriodTypeEnum.REGULAR,
          plannedDate: date,
          _id: newJobId,
        });
      }),
    );

    await Promise.all(createPromises);
  }

  async updateScheduleJob(
    jobId: string,
    isOverrideRegular: boolean,
    platform: string,
  ) {
    const scheduleJob = await this.jobModel
      .findOne({
        _id: jobId,
      })
      .lean();

    if (!scheduleJob) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    if (isOverrideRegular) {
      const scheduleJobPoints = await this.getJobPoints(jobId);

      const scheduleEmployees = await this.getEmployees(jobId);

      // Just update job with status OPEN and type REGULAR
      const regularJobs = await this.jobModel
        .find({
          fIdentifier: scheduleJob.fIdentifier,
          status: JobStatusEnum.OPEN,
          type: JobPeriodTypeEnum.REGULAR,
        })
        .lean();

      const ids = regularJobs.map((job) => job._id);

      const limit = pLimit(1);

      const updatePromises = ids.map((id) =>
        limit(async () => {
          const job = await this.jobModel
            .findOne({
              _id: id,
            })
            .lean();

          if (!job) {
            return;
          }

          // Delete all old Job Points & Job Employees
          await this.jobPointModel.deleteMany({
            job: job._id,
          });
          await this.jobEmployeeModel.deleteMany({
            job: job._id,
          });

          const jobPoints = scheduleJobPoints.map(
            ({
              _id,
              createdAt: _createdAt,
              updatedAt: _updatedAt,
              ...rest
            }) => {
              return {
                ...rest,
                job: job._id,
              };
            },
            [],
          );

          const employees = scheduleEmployees.map((e) => {
            return {
              employee: e.employee,
              estimatedHours: e.estimatedHours,
              job: job._id,
            };
          });

          await this.jobPointModel.insertMany(jobPoints);
          await this.jobEmployeeModel.insertMany(employees);

          const payload = omit(scheduleJob, [
            '_id',
            'status',
            'type',
            'jobType',
            'identifier',
            'plannedDate',
            'createdAt',
            'updatedAt',
          ]);

          return this.jobModel.updateOne(
            {
              _id: job._id,
            },
            {
              ...payload,
            },
          );
        }),
      );

      await Promise.all(updatePromises);
    }

    return this.findOne({ id: jobId, platform });
  }

  async generateScheduleJob(payload?: { fakeStartDate?: string }) {
    const currentDateDjs = payload?.fakeStartDate
      ? dayjs(payload?.fakeStartDate).utc().startOf('days')
      : dayjs().utc().startOf('days');

    // Find all periodic job
    const schedules = await this.jobModel
      .find({
        type: JobPeriodTypeEnum.PERIODIC,
        fEndDate: {
          $gt: currentDateDjs.toISOString(),
        },
        isDeleted: false,
        isActive: true,
      })
      .lean();

    const limit = pLimit(1);

    const createPromises = schedules.map((schedule) =>
      limit(async () => {
        const latestRegularJob = await this.jobModel
          .findOne({
            fIdentifier: schedule.fIdentifier,
            isDeleted: false,
          })
          .sort({
            plannedDate: -1,
          })
          .lean();

        const {
          fIdentifier,
          fRule,
          fDays,
          fInterval,
          fStartDate,
          fDayInMonth,
          fEndDate,
        } = schedule;

        let latestPlannedDateDjs = dayjs();
        let startGenerateDateDjs = dayjs();
        let endGenerateDateDjs = dayjs();

        if (latestRegularJob) {
          const { plannedDate } = latestRegularJob;
          latestPlannedDateDjs = dayjs(plannedDate).utc();
          startGenerateDateDjs = latestPlannedDateDjs
            .add(fInterval!, TypeRuleDayJS[fRule!] as ManipulateType)
            .startOf(
              fRule! === JobFRuleEnum.DAILY || !fDays?.length
                ? 'days'
                : 'months',
            );
        } else {
          latestPlannedDateDjs = dayjs(fStartDate).utc().startOf('days');
          startGenerateDateDjs = dayjs(latestPlannedDateDjs).utc();
        }

        const diffRange = latestPlannedDateDjs.diff(currentDateDjs, 'month');

        if (diffRange === 0 || MONTH_GENERATE - diffRange <= 0) {
          return;
        }

        const monthOfStartGenerateDate = startGenerateDateDjs.format('M');
        const monthOfLatestDateInspection = latestPlannedDateDjs.format('M');
        const yearOfStartGenerateDate = startGenerateDateDjs.format('YYYY');
        const currentYear = currentDateDjs.format('YYYY');

        endGenerateDateDjs = startGenerateDateDjs
          .add(MONTH_GENERATE - diffRange - 1, 'month')
          .endOf('months')
          .endOf('days');

        if (
          Number(monthOfStartGenerateDate) <=
            Number(monthOfLatestDateInspection) &&
          yearOfStartGenerateDate === currentYear
        ) {
          endGenerateDateDjs = endGenerateDateDjs.add(1, 'month');
        }

        await this.createScheduleJob(schedule._id.toString(), {
          fIdentifier,
          fInterval,
          fRule,
          fDays,
          fDayInMonth,
          fStartDate,
          fEndDate,
          startGenerateDate: startGenerateDateDjs.toISOString(),
          endGenerateDate: endGenerateDateDjs.toISOString(),
        } as any);
      }),
    );

    await Promise.all(createPromises);
  }

  async updateScheduleJobSettings(data: UpdateScheduleJobSettingsDto) {
    const { id, isRemoveRegular, ...rest } = data;

    const foundJob = (await this.jobModel
      .findByIdAndUpdate(
        id,
        {
          ...rest,
        },
        {
          new: true,
        },
      )
      .lean()) as unknown as JobDocument;

    if (!foundJob) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    if (isRemoveRegular) {
      const regularJobs = await this.jobModel
        .find({
          fIdentifier: foundJob.fIdentifier,
          status: JobStatusEnum.OPEN,
          type: JobPeriodTypeEnum.REGULAR,
        })
        .lean();

      const ids = regularJobs.map((job) => job._id);

      await this.jobModel.updateMany(
        {
          _id: { $in: ids },
        },
        {
          isDeleted: true,
          deletedAt: new Date(),
        },
      );
    }

    const {
      fIdentifier,
      fInterval,
      fRule,
      fDays,
      fDayInMonth,
      fStartDate,
      fEndDate,
    } = foundJob;

    await this.createScheduleJob(id, {
      fIdentifier,
      fInterval,
      fRule,
      fDays,
      fDayInMonth,
      fStartDate,
      fEndDate,
    } as any);

    return this.findOne({
      id,
    });
  }

  async createJobPoints(points: CreateJobPointDto[]) {
    return this.jobPointModel.bulkWrite(
      points.map((point) => ({
        insertOne: {
          document: point,
        },
      })),
    );
  }

  async getJobPoints(jobId: string) {
    return this.jobPointModel.find({ job: jobId }).lean();
  }

  async getEmployees(jobId: string) {
    return this.jobEmployeeModel.find({ job: jobId }).lean();
  }

  async getJobEquipments(jobId: string) {
    return this.jobEquipmentModel.find({ job: jobId }).lean();
  }

  async viewSummaryJob(payload: ViewSummaryJobDto) {
    const { id, platform } = payload;
    const jobs = await this.jobModel
      .aggregate(aggregateOfViewSummaryOfJob(id, platform, payload.user))
      .exec();

    const job = jobs.length > 0 ? jobs[0] : null;
    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    let statusAllowedViewSummary = ['ready', 'closed', 'completed'];

    if (platform === 'mb') {
      statusAllowedViewSummary = ['in_progress', 'closed', 'completed'];
    }

    if (!statusAllowedViewSummary.includes(job.status)) {
      throw new BadRequestException(JOB_KEYS.JOB_STATUS_NOT_ALLOW_VIEW_SUMMARY);
    }

    const jobPoints = job.jobPoints ?? [];

    let totalGreenResult = 0;
    let totalYellowResult = 0;
    let totalRedResult = 0;

    jobPoints.forEach((jobPoint) => {
      if (jobPoint.status === JobPointStatusEnum.GREEN) {
        totalGreenResult += 1;
      }

      if (jobPoint.status === JobPointStatusEnum.YELLOW) {
        totalYellowResult += 1;
      }

      if (jobPoint.status === JobPointStatusEnum.RED) {
        totalRedResult += 1;
      }
    });

    const rootUnit = await this.unitService.findRootUnitOfLocation(
      job.location.toString(),
    );

    const jobPointsGroupByUnitSorted = GroupJobPointsBySortedUnit(
      jobPoints,
      rootUnit,
    );

    return {
      job: job,
      employees: job.employees,
      units: jobPointsGroupByUnitSorted,
      totalGreenResult,
      totalYellowResult,
      totalRedResult,
    };
  }

  async checkOverlapJob(payload: CheckOverlapJobDto) {
    const {
      employees,
      equipments = [],
      equipmentType,
      timezone = '+00:00',
    } = payload;

    const employeeIds = employees.map((emp) => emp.employee);

    const uniqEmployeeIds = uniq(employeeIds);

    const employeesDbs = await this.tenantUserModel
      .find({
        _id: { $in: uniqEmployeeIds },
      })
      .select('oddWeeks evenWeeks displayName')
      .lean();

    // Checking planned date overlap with working days in oddWeek or evenWeek of employees
    // E.g: Employee A work on Monday, Wednesday, Friday in oddWeek
    // If planned date is Monday, Wednesday, Friday in oddWeek => overlap
    // If planned date is Tuesday, Thursday in oddWeek => not overlap
    // Will return list of employees and equipments that overlap with planned date

    const overlapEmployees: any[] = [];

    employees.forEach((employee) => {
      const employeeDb = employeesDbs.find(
        (emp) => emp._id.toString() === employee.employee,
      );

      if (!employeeDb) {
        return;
      }

      const isOddWeek = isOddWeekOfYear(employee.plannedDate, timezone);
      const workingDays = isOddWeek
        ? employeeDb.oddWeeks.map((day) => day.toLowerCase())
        : employeeDb.evenWeeks.map((day) => day.toLowerCase());

      if (
        !workingDays.includes(
          dayjs(employee.plannedDate).format('ddd').toLowerCase(),
        )
      ) {
        overlapEmployees.push({
          _id: employee.employee,
          plannedDate: employee.plannedDate,
          displayName: employeeDb.displayName,
          oddWeeks: employeeDb.oddWeeks,
          evenWeeks: employeeDb.evenWeeks,
        });
      }
    });

    const equipmentGroupByPlannedDates = groupBy(equipments, (eq) =>
      dayjs(eq.plannedDate).utc().startOf('day'),
    );

    const payloadEquipmentWithPlannedDates = Object.keys(
      equipmentGroupByPlannedDates,
    ).map((plannedDate) => {
      return {
        plannedDate,
        equipments: equipmentGroupByPlannedDates[plannedDate].map(
          (eq) => eq.equipment,
        ),
        equipmentType,
        timezone,
        jobId: payload.jobId,
      };
    });

    const overlapEquipments: any[] = [];

    const promises = payloadEquipmentWithPlannedDates.map(async (payload) => {
      const availableEquipments =
        await this.equipmentService.getAvailableEquipments(payload);

      const equipmentIds = payload.equipments.map((eq) => new ObjectId(eq));
      const equipmentsDbs = await this.equipmentModel
        .find({
          _id: { $in: equipmentIds },
          type: equipmentType ?? { $exists: true },
        })
        .select('type description isActive')
        .lean();
      const overlaps = availableEquipments.length
        ? differenceBy(equipmentsDbs, availableEquipments, (e) =>
            e._id.toString(),
          )
        : availableEquipments;
      if (overlaps.length) {
        overlaps.forEach((equipment) => {
          overlapEquipments.push({
            _id: equipment._id.toString(),
            plannedDate: dayjs(payload.plannedDate).toISOString(),
            type: equipment.type,
            description: equipment.description,
            isActive: equipment.isActive,
          });
        });
      }
    });

    await Promise.all(promises);

    // If equipments in dbs different with available equipments => overlap
    // const overlapEquipments = availableEquipments.length
    //   ? differenceBy(equipmentsDbs, availableEquipments, (e) =>
    //       e._id.toString(),
    //     )
    //   : availableEquipments;

    return {
      employees: uniq(overlapEmployees),
      equipments: uniq(overlapEquipments),
    };
  }

  async coWorkerSyncDataOfJob(payload: MobileCoWorkerSyncDataOfJobDto) {
    const { id, actualHours, feedbacks, fuaDescriptions, platform, user } =
      payload;
    if (payload.platform !== 'mb') {
      throw new BadRequestException(JOB_KEYS.PLATFORM_NOT_SUPPORT);
    }
    const job = await this.jobModel.findById(id).lean();

    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const statusAllowSyncData = [JobStatusEnum.IN_PROGRESS];

    if (!statusAllowSyncData.includes(job.status)) {
      throw new BadRequestException(JOB_KEYS.JOB_STATUS_NOT_ALLOW_SYNC_DATA);
    }

    if (actualHours) {
      const jobEmployees = actualHours.map((item) => {
        return {
          job: job._id,
          employee: user,
          actualHours: item.hour,
          plannedDate: dayjs(item.plannedDate).utc().startOf('day'),
        };
      });

      await this.jobEmployeeModel.bulkWrite(
        jobEmployees.map((jobEmployee) => ({
          updateOne: {
            filter: {
              job: jobEmployee.job,
              employee: jobEmployee.employee,
              plannedDate: jobEmployee.plannedDate,
            },
            update: {
              $set: {
                actualHours: jobEmployee.actualHours,
              },
            },
            upsert: true,
          },
        })),
      );
    }

    if (feedbacks || fuaDescriptions) {
      await this.jobModel.updateOne(
        {
          _id: job._id,
        },
        {
          feedbacks: feedbacks ?? job.feedbacks,
          fuaDescriptions: fuaDescriptions ?? job.fuaDescriptions,
          updatedBy: user,
        },
      );
    }

    return this.findOne({ id, platform, user });
  }

  async getPdfReport(payload: GetPdfReportJobDto) {
    const { id, headers } = payload;
    const job = await this.jobModel.findById(id).lean();
    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const pdfPublicUrl = job.pdfPublicUrl;
    let generatePdfStatus = job.generatePdfStatus;
    if (!generatePdfStatus) {
      generatePdfStatus = GeneratePdfStatusEnum.NONE;
    }
    if (!pdfPublicUrl && generatePdfStatus !== GeneratePdfStatusEnum.PENDING) {
      firstValueFrom(
        this.pdfClient.send(
          { cmd: JOB_MESSAGES.GET_PDF_REPORT_OF_JOB },
          {
            id: id,
            headers,
          },
        ),
      )
        .then(() => console.log('Call to PDF to trigger generate PDF'))
        .catch((error) => console.log(error));
      return {
        generatePdfStatus: GeneratePdfStatusEnum.PENDING,
        pdfPublicUrl: '',
        message: 'System still pending generate pdf report of job',
      };
    } else if (
      !pdfPublicUrl &&
      generatePdfStatus === GeneratePdfStatusEnum.PENDING
    ) {
      return {
        generatePdfStatus: GeneratePdfStatusEnum.PENDING,
        pdfPublicUrl: '',
        message: 'System still pending generate pdf report of job',
      };
    } else if (generatePdfStatus === GeneratePdfStatusEnum.DONE) {
      return {
        generatePdfStatus,
        pdfPublicUrl,
      };
    }

    return {
      generatePdfStatus,
      pdfPublicUrl,
    };
  }

  async deleteScheduleJob(payload: DeleteScheduleJobDto) {
    if (payload.platform === 'mb') {
      throw new BadRequestException(JOB_KEYS.PLATFORM_NOT_SUPPORT);
    }

    const periodicJob = await this.jobModel
      .findOne({
        _id: payload.jobId,
        type: JobPeriodTypeEnum.PERIODIC,
        isDeleted: false,
      })
      .lean();

    if (!periodicJob) {
      throw new BadRequestException(JOB_KEYS.JOBS_NOT_FOUND);
    }

    const deletedJobIds: string[] = [periodicJob._id.toString()];

    if (!payload.isKeepAllRegularJobs) {
      const regularJobs = await this.jobModel
        .find({
          type: JobPeriodTypeEnum.REGULAR,
          fIdentifier: periodicJob.fIdentifier,
          status: JobStatusEnum.OPEN,
          isDeleted: false,
        })
        .lean();

      const regularJobIds = regularJobs.map((regularJob) =>
        regularJob._id.toString(),
      );

      deletedJobIds.push(...regularJobIds);
    }

    return await this.deleteJobs({
      jobIds: deletedJobIds,
      platform: payload.platform,
      user: payload.user,
    });
  }

  async detailsPlanningJob(id: string) {
    return (
      await this.jobModel.aggregate([
        { $match: { _id: new Types.ObjectId(id) } },
        {
          $lookup: {
            from: 'jobs',
            localField: 'fIdentifier',
            foreignField: 'fIdentifier',
            pipeline: [
              {
                $match: {
                  type: JobPeriodTypeEnum.PERIODIC,
                },
              },
              {
                $project: { _id: 1 },
              },
            ],
            as: 'scheduler',
          },
        },
        { $unwind: { path: '$scheduler', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'jobemployees',
            localField: '_id',
            foreignField: 'job',
            as: 'employees',
          },
        },
        {
          $project: {
            _id: 1,
            identifier: 1,
            title: 1,
            status: 1,
            fIdentifier: 1,
            locationInfo: 1,
            plannedDate: 1,
            instructions: 1,
            isOverdue: 1,
            scheduler: '$scheduler._id',
            jobType: 1,
            employees: {
              $map: {
                input: '$employees',
                as: 'employee',
                in: '$$employee.employee',
              },
            },
          },
        },
      ])
    )[0];
  }

  async updatePlanningJob(payload: UpdatePlanningJobDto) {
    const isInThePast = dayjs(payload.plannedDate)
      .utc()
      .isBefore(dayjs().utc(), 'day');
    if (isInThePast) {
      throw new BadRequestException(
        JOB_KEYS.PLANNED_DATE_MUST_BE_GREATER_OR_EQUAL_THAN_NOW,
      );
    }

    const job = await this.jobModel.findById(payload.id);

    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    if (
      job.status !== JobStatusEnum.OPEN &&
      job.status !== JobStatusEnum.IN_PROGRESS
    ) {
      throw new BadRequestException(JOB_KEYS.JOB_STATUS_NOT_ALLOW_UPDATE);
    }

    await this.jobModel.updateOne(
      { _id: payload.id },
      {
        plannedDate: payload.plannedDate,
        ...(dayjs(payload.plannedDate).isAfter(dayjs().utc().startOf('day'))
          ? { isOverdue: false }
          : {}),
      },
    );

    return this.detailsPlanningJob(payload.id);
  }

  async getEmployeesSchedules(
    period: {
      startDate: Date;
      endDate: Date;
      timezone: string;
    },
    employees: Types.ObjectId[],
    location?: string,
  ) {
    return this.jobModel.aggregate(
      buildEmployeesSchedulesAggregate(period, employees, location),
      { allowDiskUse: true },
    );
  }

  async getEquipmentSchedules(
    period: {
      startDate: Date;
      endDate: Date;
      timezone: string;
    },
    equipmentType: EquipmentEnum,
    location?: string,
  ) {
    return this.jobModel.aggregate(
      buildEquipmentSchedulesAggregate(period, equipmentType, location),
      { allowDiskUse: true },
    );
  }

  //#endregion

  //#region  Private function

  private async updateActualHoursForEmployees(
    actualHours: any,
    jobId: string,
    userId: string,
  ) {
    const updateJobEmployees = actualHours.map((actualHour) => {
      return {
        job: jobId,
        employee: userId,
        actualHours: actualHour.hour,
        plannedDate: dayjs(actualHour.plannedDate).utc().startOf('day'),
      };
    });

    await this.jobEmployeeModel.bulkWrite(
      updateJobEmployees.map((update) => ({
        updateOne: {
          filter: {
            job: update.job,
            employee: update.employee,
            plannedDate: update.plannedDate,
          },
          update: {
            actualHours: update.actualHours,
          },
        },
      })),
    );
  }

  private async processActualHoursUpdate(
    platform: string,
    actualHours: any,
    jobId: string,
    userId: string,
  ): Promise<void> {
    if (platform !== 'portal' && actualHours && actualHours.length > 0) {
      await this.updateActualHoursForEmployees(actualHours, jobId, userId);
    }
  }

  private async processAssigneeChange(
    currentAssignee: any,
    newAssignee: any,
    jobId: string,
    tenantId: string,
  ): Promise<void> {
    if (newAssignee && newAssignee.toString() !== currentAssignee.toString()) {
      this.sendEmailWhenAssigneeJob(jobId, tenantId)
        .then(() => {
          this.logger.log('Send email to assignee successfully');
        })
        .catch((error) => {
          this.logger.error('Send email to assignee failed with error', error);
        });
    }
  }

  private async processReservations(
    reserved: any,
    jobId: string,
    userId: string,
  ): Promise<void> {
    if (reserved) {
      await this.handleReservedUpdates(reserved, jobId, userId);
    }
  }

  private async handleReservedUpdates(
    reserved: any,
    jobId: string,
    user: string,
  ) {
    if (!reserved) return;

    if (!reserved._id) {
      await firstValueFrom(
        this.inventoryClient.send(
          { cmd: RESERVED_MESSAGES.CREATE_RESERVED },
          {
            body: {
              ...reserved,
              job: jobId,
            },
            user,
          },
        ),
      );
    } else {
      await firstValueFrom(
        this.inventoryClient.send(
          { cmd: RESERVED_MESSAGES.UPDATE_RESERVED },
          {
            body: {
              ...reserved,
            },
            user,
          },
        ),
      ).catch((exception) => {
        throw new BadRequestException({
          message: exception.error.message,
          errors: exception.error.errors,
        });
      });
    }
  }

  private async processUnitChanges(
    job: any,
    units: any,
    platform: string,
  ): Promise<string[]> {
    if (!units) {
      return job.units;
    }

    const { updatedUnitIds } = await this.validateJobUnits(
      job,
      units,
      platform,
    );

    return updatedUnitIds.length > 0 ? updatedUnitIds : job.units;
  }

  private async validateJobUnits(job: any, units: any[], platform: string) {
    if (!units) return { updatedUnitIds: [] };

    await this.validateUnitsOfPayload(job.location.toString(), units);

    const unitsPayload = removeUnitsDoesNotHavePoints(units);
    const updatedUnitIds = unitsPayload.map((unit) => unit._id.toString());
    const isDifference =
      difference(
        updatedUnitIds,
        job.units?.map((val) => val._id.toString()),
      ).length > 0 || updatedUnitIds.length !== job.units.length;

    if (job.status !== JobStatusEnum.OPEN && job.units && isDifference) {
      throw new BadRequestException(JOB_KEYS.NOT_ALLOW_UPDATE_UNITS);
    }

    await this.updateJobPoints(unitsPayload, job, platform);

    if (job.status !== JobStatusEnum.OPEN) {
      const reservations = unitsPayload.flatMap((unit) => unit.reservations);
      if (reservations.length > 0) {
        const updateJobCheckInCheckOutPromise = reservations.map(
          async (reservation) => {
            await this.nightRegistrationService.updateJobCheckInOrCheckOut(
              reservation,
              job._id.toString(),
            );
          },
        );

        await Promise.all(updateJobCheckInCheckOutPromise);
      }
    }

    return { unitsPayload, updatedUnitIds, isDifference };
  }

  private async prepareJobUpdateData(job: any, payload: any) {
    // Prepare status and overdue flag
    const status =
      payload.status === JobStatusEnum.REJECT ? payload.status : job.status;
    const isOverdue = payload.isOverdue;

    // Validate payload data using schema validation
    const validatedPayload = validateUpdateData({
      ...omit(payload, ['isOverdue']),
      status,
      currentType: job.type,
    });

    const {
      units,
      user,
      employees = [],
      reserved,
      isOverrideRegular,
      equipments,
      ...rest
    } = validatedPayload;

    // Validate basic requirements
    if (
      rest.reportType &&
      rest.reportType !== JobReportTypeEnum.INTERNAL &&
      !rest.reportContact
    ) {
      throw new BadRequestException(JOB_KEYS.REPORT_CONTACT_REQUIRED);
    }

    validateImages(rest.images);

    // Validate employees and equipment
    const employeeIds = employees.map((emp) => emp.employee);
    const uniqueEmployeeIds = uniq(employeeIds);
    await this.validateEmployees(
      rest.assignee,
      uniqueEmployeeIds as string[],
      job.jobType,
    );

    const equipmentIds = equipments?.map((val) => val.equipment);
    const uniqueEquipmentIds = uniq(equipmentIds);

    await this.validateEquipments(uniqueEquipmentIds as string[]);

    await this.validatePlanner(rest.planner, job.jobType);

    // Get contact information
    if (rest.reportType) {
      rest.reportContact = await this.getValidReportContact(
        rest.reportType,
        rest.reportContact,
      );
    }

    return {
      status,
      isOverdue,
      units,
      user,
      employees,
      reserved,
      isOverrideRegular,
      equipments,
      rest,
    };
  }

  private async MapCheckInAndCheckOutToJobPoint(
    unitWithJobPoints: any[],
    jobId,
  ) {
    const promises = unitWithJobPoints.map(async (unitWithJobPoint) => {
      const reservations =
        await this.nightRegistrationService.getLastCheckInAndLastCheckOutOfUnit(
          unitWithJobPoint._id,
          jobId,
        );
      return {
        ...unitWithJobPoint,
        reservations: reservations,
      };
    });

    return await Promise.all(promises);
  }

  private async deleteOldImages(
    oldImages: string[] | undefined,
    newImages: string[] | undefined,
    headers: any,
  ) {
    if (oldImages && newImages) {
      const differenceImages = difference(oldImages, newImages);

      const deletedImages = await this.uploadFileModel
        .find({
          publicUrl: { $in: differenceImages },
        })
        .lean();

      await Promise.all(
        deletedImages.map((deletedImage) => {
          const url = `${process.env.UPLOAD_SERVICE_URL}/delete/${deletedImage._id}`;
          const {
            host: _,
            connection: __,
            'content-length': ____,
            ...forwardHeaders
          } = headers;
          firstValueFrom(
            this.httpService.delete(url, {
              httpAgent: new https.Agent({
                rejectUnauthorized: false,
              }),
              headers: forwardHeaders,
            }),
          );
        }),
      );
    }
  }

  private async validateEquipments(equipmentIds: string[]) {
    if (!equipmentIds) {
      return;
    }

    const equipments = await this.equipmentModel
      .find({
        _id: { $in: equipmentIds },
      })
      .lean();

    if (equipments.length !== equipmentIds.length) {
      throw new BadRequestException(JOB_KEYS.EQUIPMENTS_NOT_FOUND);
    }
  }

  private async getValidReportContact(reportType, reportContact) {
    let contact: any;
    switch (reportType) {
      case JobReportTypeEnum.CUSTOMER:
        contact = await this.contactService.getValidContact(
          reportContact,
          ContactRole.DEBTOR,
        );
        break;
      case JobReportTypeEnum.SUPPLIER:
        contact = await this.contactService.getValidContact(
          reportContact,
          ContactRole.SUPPLIER,
        );
        break;
      case JobReportTypeEnum.INTERNAL:
        contact = await this.contactModel
          .findOne({
            isInternal: true,
          })
          .lean();
        break;
      default:
        break;
    }
    if (!contact) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND_CONTACT);
    }
    return contact._id;
  }

  private async validateUnitsOfPayload(locationId: string, units: any[]) {
    const unitOfLocation = await this.unitModel.find({ location: locationId });

    const unitIdsOfLocation = unitOfLocation.map((unit) => unit._id.toString());

    const unitIdsOfPayload = units.map((unit) => unit._id);

    const filterUnitId = difference(unitIdsOfPayload, unitIdsOfLocation);
    if (filterUnitId.length > 0) {
      throw new BadRequestException(JOB_KEYS.UNITS_ARE_NO_PART_OF_LOCATION);
    }
  }

  private async validateEmployees(
    assignee: string,
    employeeIds: string[],
    jobType: JobTypeEnum,
  ) {
    if (!assignee || employeeIds.length === 0) {
      return;
    }

    if (!employeeIds.includes(assignee)) {
      throw new BadRequestException(JOB_KEYS.EMPLOYEE_MUST_HAVE_ASSIGNEE);
    }

    switch (jobType) {
      case JobTypeEnum.INSPECTION:
        await this.validateRoleOfEmployees(
          employeeIds,
          TenantRoleEnum.INSPECTOR,
        );
        break;
      case JobTypeEnum.MAINTENANCE:
        await this.validateRoleOfEmployees(
          employeeIds,
          TenantRoleEnum.MECHANIC,
        );
        break;
      case JobTypeEnum.CLEANING:
        await this.validateRoleOfEmployees(employeeIds, TenantRoleEnum.CLEANER);
        break;
      default:
        break;
    }
  }

  private async validateReviewer(userId: string, jobType: JobTypeEnum) {
    switch (jobType) {
      case JobTypeEnum.INSPECTION:
        await this.validateRoleOfReviewer(userId, [
          TenantRoleEnum.INSPECTION_REVIEWER_ALL_INSPECTIONS,
        ]);
        break;
      case JobTypeEnum.MAINTENANCE:
        await this.validateRoleOfReviewer(userId, [
          TenantRoleEnum.MAINTENANCE_REVIEWER,
        ]);
        break;
      case JobTypeEnum.CLEANING:
        await this.validateRoleOfReviewer(userId, [
          TenantRoleEnum.CLEANING_REVIEWER,
        ]);
        break;
      default:
        break;
    }
  }

  private async validatePlanner(planner: string, jobType: JobTypeEnum) {
    if (!planner) {
      return;
    }

    switch (jobType) {
      case JobTypeEnum.INSPECTION:
        await this.validateRoleOfPlanner(planner, [
          TenantRoleEnum.INSPECTION_PLANNER,
          TenantRoleEnum.INSPECTOR,
        ]);
        break;
      case JobTypeEnum.MAINTENANCE:
        await this.validateRoleOfPlanner(planner, [
          TenantRoleEnum.MAINTENANCE_PLANNER,
          TenantRoleEnum.MECHANIC,
        ]);
        break;
      case JobTypeEnum.CLEANING:
        await this.validateRoleOfPlanner(planner, [
          TenantRoleEnum.CLEANING_PLANNER,
          TenantRoleEnum.CLEANER,
        ]);
        break;
      default:
        break;
    }
  }

  private async validateRoleOfReviewer(
    reviewer: string,
    roles: TenantRoleEnum[],
  ) {
    const isValid = await this.tenantUserService.verifyRoles(reviewer, roles);

    if (!isValid) {
      throw new BadRequestException(JOB_KEYS.REVIEWER_DOES_NOT_HAVE_PERMISSION);
    }
  }

  private async validateRoleOfPlanner(
    planner: string,
    roles: TenantRoleEnum[],
  ) {
    const isValid = await this.tenantUserService.verifyRoles(planner, roles);

    if (!isValid) {
      throw new BadRequestException(JOB_KEYS.PLANNER_DOES_NOT_HAVE_PERMISSION);
    }
  }

  private async validateRoleOfUser(user: string, roles: TenantRoleEnum[]) {
    return await this.tenantUserService.verifyRoles(user, roles);
  }

  private async getJobTypesWhenGetAllJobForMobile(
    jobTypes: JobTypeEnum[],
    user: string,
    role: TenantRoleEnum,
    jobTypeEnum: JobTypeEnum,
  ) {
    const isValid = await this.validateRoleOfUser(user, [role]);
    if (!isValid) {
      const indexOfInspection = jobTypes.indexOf(jobTypeEnum);
      jobTypes.splice(indexOfInspection, 1);
    }
  }

  private async validateRoleOfEmployees(
    employeeIds: string[],
    role: TenantRoleEnum,
  ) {
    const validateEmployeePermissions = await Promise.all(
      employeeIds.map((id) => this.tenantUserService.verifyRoles(id, [role])),
    );

    if (validateEmployeePermissions.some((val) => !val)) {
      throw new BadRequestException(
        JOB_KEYS.ASSIGNEE_OR_EMPLOYEEES_DOES_NOT_HAVE_PERMISSION,
      );
    }
  }

  private async updateJobPoints(
    unitsPayload: any[],
    job: any,
    platform: string,
  ) {
    switch (job.status) {
      case JobStatusEnum.OPEN:
        if (platform === 'mb') {
          throw new BadRequestException(
            JOB_KEYS.MOBILE_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_OPEN_STATUS,
          );
        } else {
          const points = flatMap(unitsPayload, (unit) => {
            validteAndSortPosition(unit.points, ModuleNameEnum.JOB, true);
            return unit.points.map((point) => {
              return {
                ...point,
                unit: unit._id,
                job: job._id,
              };
            });
          });

          if (points.length > 0) {
            const willCreatedPoints = points.filter((point) => !point._id);
            const willUpdatedPoints = points.filter((point) => point._id);
            const existedIds = willUpdatedPoints.map((point) => point._id);

            await this.jobPointModel.deleteMany({
              _id: { $nin: existedIds },
              job: job._id,
            });

            const createResult = await this.jobPointModel.bulkWrite(
              willCreatedPoints.map((point) => ({
                insertOne: {
                  document: {
                    ...point,
                  },
                },
              })),
            );

            console.log(createResult);

            await this.jobPointModel.bulkWrite(
              willUpdatedPoints.map((point) => ({
                updateOne: {
                  filter: { _id: point._id },
                  update: {
                    description: point.description,
                    position: point.position,
                  },
                },
              })),
            );
          }
        }
        break;
      case JobStatusEnum.IN_PROGRESS: {
        if (platform !== 'mb') {
          throw new BadRequestException(
            JOB_KEYS.PORTAL_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_STATUS_IN_PROGRESS,
          );
        }

        await this.bulkWriteJobPoints(unitsPayload, job, 'mb');
        break;
      }
      case JobStatusEnum.READY: {
        if (platform === 'mb') {
          throw new BadRequestException(
            JOB_KEYS.MOBILE_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_READY_STATUS,
          );
        }

        await this.bulkWriteJobPoints(unitsPayload, job);
        break;
      }
      default:
        break;
    }
  }

  private async bulkWriteJobPoints(
    unitsPayload: any[],
    job: any,
    platform: string = 'portal',
  ) {
    const points = flatMap(unitsPayload, (unit) => {
      return unit.points.map((point) => {
        if (
          [JobPointStatusEnum.YELLOW, JobPointStatusEnum.RED].includes(
            point.status,
          ) &&
          (!point.notes || !point.images.length)
        ) {
          throw new BadRequestException(
            JOB_KEYS.JOB_POINTS_MISS_NOTES_OR_IMAGES,
          );
        }

        return {
          ...omit(point, ['description']),
          unit: unit._id,
          job: job._id,
        };
      });
    });

    const createCostLineOfPoints = flatMap(points, (point) =>
      (point.costLines ?? []).filter((costLine) => !costLine._id),
    );

    if (
      createCostLineOfPoints.length > 0 &&
      job.reportType === JobReportTypeEnum.INTERNAL &&
      platform === 'mb'
    ) {
      throw new BadRequestException(
        JOB_KEYS.CANNOT_CREATE_COSTLINE_FOR_JOB_HAVE_REPORT_TYPE_INTERNAL,
      );
    }

    const pointIdPayloads = points.map((point) => point._id);
    const pointInDbs = await this.jobPointModel
      .find({
        job: job._id,
      })
      .lean();

    if (pointInDbs.length != pointIdPayloads.length) {
      throw new BadRequestException(
        JOB_KEYS.MISS_SOME_JOB_POINTS_OR_JOB_POINTS_NOT_FOUND,
      );
    }
    const pointInDbGroupByUnits = groupBy(pointInDbs, 'unit');
    const pointInPayloadGroupByUnits = groupBy(points, 'unit');
    const unitInPayload = unitsPayload.map((unit) => unit._id);
    for (const unit of unitInPayload) {
      if (
        pointInDbGroupByUnits[unit].length !==
        pointInPayloadGroupByUnits[unit].length
      ) {
        throw new BadRequestException(
          JOB_KEYS.MISS_SOME_JOB_POINTS_OR_JOB_POINTS_NOT_FOUND,
        );
      }
    }

    if (platform !== 'mb') {
      const costTypeOfCostLines = flatMap(points, (point) =>
        (point.costLines ?? []).map((costLine) => costLine.costType),
      );

      const acceptedCostTypes = await this.costTypeModel.find({
        type: CostTypeType.JOB_OR_CUSTOM,
        isActive: true,
      });

      const acceptedCostTypeIds = acceptedCostTypes.map((acceptedCostType) =>
        acceptedCostType._id.toString(),
      );

      const isValid = costTypeOfCostLines.every((costType) =>
        acceptedCostTypeIds.includes(costType.toString()),
      );

      if (!isValid) {
        throw new BadRequestException(JOB_KEYS.INVALID_COST_TYPE);
      }
    }
    const bulkWritePromises = points.map(async (point) => {
      const pointInDb = pointInDbs.filter(
        (v) => v._id.toString() === point._id.toString(),
      );

      const costLineOfPointIds = pointInDb[0].costLines
        ? pointInDb[0].costLines.map((v: any) => v.toString())
        : [];
      const { insertedIds, updatedIds } = await this.updateCostLineOfJobPoints(
        point.costLines ?? [],
        costLineOfPointIds,
        pointInDb[0].unit,
        job,
      );

      const insertedIdArrays = Object.keys(insertedIds).map(
        (key) => insertedIds[key],
      );

      const upsertedIdArrays = Object.keys(updatedIds).map(
        (key) => updatedIds[key],
      );

      const referenceCostLineIds = [...insertedIdArrays, ...upsertedIdArrays];

      return {
        updateOne: {
          filter: { _id: point._id },
          update: {
            ...omit(point, ['unit', 'costLines']),
            costLines: referenceCostLineIds,
          },
        },
      };
    });

    const bulkWrites = await Promise.all(bulkWritePromises);

    await this.jobPointModel.bulkWrite(bulkWrites);
  }

  private async updateCostLineOfJobPoints(
    costLines: any[],
    costLineOfPointIds: string[],
    unitId: any,
    job: any,
  ) {
    const willUpdateCostLines = costLines.filter((costLine) => costLine._id);
    const willCreateCostLines = costLines.filter((costLine) => !costLine._id);

    const willUpdateCostLineIds = willUpdateCostLines.map(
      (willUpdateCostLine) => willUpdateCostLine._id,
    );

    const isValidUpdate = willUpdateCostLineIds.every((id) =>
      costLineOfPointIds.includes(id),
    );

    if (!isValidUpdate) {
      throw new BadRequestException(JOB_KEYS.COSTLINE_ARE_NOT_IN_JOB_POINT);
    }

    const willDeletedCostLineIds = costLineOfPointIds.filter(
      (costLineOfPointId) => !willUpdateCostLineIds.includes(costLineOfPointId),
    );

    validteAndSortPosition(costLines, ModuleNameEnum.JOB);

    const createdResults = await this.costLineModel.bulkWrite(
      willCreateCostLines.map((willCreateCostLine) => ({
        insertOne: {
          document: {
            ...willCreateCostLine,
            totalPrice: willCreateCostLine.quantity * willCreateCostLine.price,
            type: ContractType.JOB,
            periodType: AgreementLinePeriodType.ONE_TIME,
            startDate: dayjs().utc().toDate(),
            endDate: dayjs().utc().toDate(),
            isCustom: false,
            isCredit: false,
            generatedAt: dayjs().utc().toDate(),
            unit: unitId,
            location: job.location,
            job: job._id,
          },
        },
      })),
    );

    await this.costLineModel.bulkWrite(
      willUpdateCostLines.map((willUpdateCostLine) => ({
        updateOne: {
          filter: { _id: willUpdateCostLine._id },
          update: {
            ...willUpdateCostLine,
            unit: unitId,
            location: job.location,
            job: job._id,
            totalPrice: willUpdateCostLine.quantity * willUpdateCostLine.price,
          },
        },
      })),
    );

    if (willDeletedCostLineIds.length > 0) {
      await this.costLineModel.updateMany(
        { _id: { $in: willDeletedCostLineIds } },
        { isDeleted: true },
      );
    }

    return {
      insertedIds: createdResults.insertedIds,
      updatedIds: willUpdateCostLineIds,
    };
  }

  private async createFollowUpJobs(
    jobId: string,
    userId: string,
    tenantId: string,
  ) {
    const job = (await this.jobModel
      .findById(jobId)
      .lean()) as unknown as JobDocument;

    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const payload = await this.getFollowUpJobData(job, userId);

    const jobPoints = await this.getJobPoints(jobId);

    const flattenByActions = flatMap(jobPoints, (point) => {
      return point.actions?.map((action) => {
        return {
          ...action,
          unit: point.unit.toString(),
        };
      });
    });
    const groupedByTypeActions = groupBy(flattenByActions, 'type');

    const newFollowUpJobs: any[] = [];

    const groupTypes = Object.keys(groupedByTypeActions);

    for (const groupType of groupTypes) {
      const actions = groupedByTypeActions[groupType] as JobPointAction[];

      const groupedActions = groupActions(actions);

      for (const group of groupedActions) {
        const isGrouped = group.some((action) => action.isGrouped);
        const jobType = group[0].type;

        const title = isGrouped
          ? `Grouped follow up action of ${job.title}`
          : `Follow up action of ${job.title}`;

        const identifier = await this.identifierService.generateIdentifier(
          IdentifierType.JOB,
        );

        const convertedGroup = group as any[];
        const convertUnitIds = convertedGroup.map((action) => action.unit);

        const data = {
          ...payload,
          identifier,
          jobType,
          title,
          units: uniq(convertUnitIds),
        };
        console.log('🚀 ~ JobService ~ createFollowUpJobs ~ data:', data);

        const followUpJob = await this.jobModel.create(data);

        const jobPoints = convertedGroup.map((action) => ({
          description: action.description,
          position: 0,
          unit: action?.unit,
          job: followUpJob._id.toString(),
          images: action.images,
        }));

        const employees = data.employees.map((e) => ({
          employee: e.employee,
          estimatedHours: 0,
          job: followUpJob._id.toString(),
        }));

        await this.createJobPoints(jobPoints);

        await this.jobEmployeeModel.insertMany(employees);

        newFollowUpJobs.push({
          _id: followUpJob._id,
          identifier: followUpJob.identifier,
          title: followUpJob.title,
          jobType: followUpJob.jobType,
        });

        this.sendEmailWhenAssigneeJob(
          followUpJob._id.toString(),
          tenantId,
        ).catch((error) => {
          this.logger.error(error);
        });
      }
    }

    return newFollowUpJobs;
  }

  private async getFollowUpJobData(job: JobDocument, userId: string) {
    const jobEmployees = await this.jobEmployeeModel
      .find({ job: job._id })
      .select('employee estimatedHours')
      .lean();

    const employees = [
      ...jobEmployees.map((e) => ({
        employee: e.employee.toString(),
        estimatedHours: 0,
      })),
      { employee: job.planner?.toString(), estimatedHours: 0 },
    ];

    const uniqueEmployees = uniqBy(employees, 'employee');

    return {
      type: job.type,
      jobType: job.jobType,
      instructions: job.instructions,
      reportType: job.reportType,
      reportContact: job.reportContact,
      images: job.images,
      assignee: job.planner,
      planner: job.planner,
      equipments: job.equipments,
      status: JobStatusEnum.OPEN,
      plannedDate: dayjs().utc().startOf('day').toISOString(),
      createdBy: userId,
      locationInfo: job.locationInfo,
      locationTeamInfo: job.locationTeamInfo,
      assigneeInfo: job.plannerInfo,
      plannerInfo: job.plannerInfo,
      reportContactInfo: job.reportContactInfo,
      employees: uniqueEmployees,
      title: '',
      units: job.units,
      location: job.location,
    };
  }

  private async validatePayloadDataWhenCompleteJob(
    payload: any,
    jobReportType: JobReportTypeEnum,
  ) {
    let invoiceContact: any;
    let countRtContacts: any;
    switch (jobReportType) {
      case JobReportTypeEnum.CUSTOMER:
        invoiceContact = await this.contactModel.findOne({
          _id: payload.invoiceContact,
          contactRole: ContactRole.DEBTOR,
        });

        if (!invoiceContact) {
          throw new BadRequestException(JOB_KEYS.INVOICE_CONTACT_NOT_FOUND);
        }

        if (payload.rtContacts && payload.rtContacts.length > 0) {
          countRtContacts = await this.contactModel.countDocuments({
            _id: {
              $in: payload.rtContacts,
            },
            contactRole: {
              $in: [ContactRole.DEBTOR, ContactRole.PERSON],
            },
            isInternal: false,
          });

          if (countRtContacts !== payload.rtContacts.length) {
            throw new BadRequestException(
              JOB_KEYS.REPORT_TO_CONTACTS_NOT_FOUND,
            );
          }
        }
        break;
      case JobReportTypeEnum.SUPPLIER:
        if (payload.rtContacts && payload.rtContacts.length > 0) {
          countRtContacts = await this.contactModel.countDocuments({
            _id: {
              $in: payload.rtContacts,
            },
            contactRole: {
              $in: [ContactRole.SUPPLIER, ContactRole.PERSON],
            },
            isInternal: false,
          });

          if (countRtContacts !== payload.rtContacts.length) {
            throw new BadRequestException(
              JOB_KEYS.REPORT_TO_CONTACTS_NOT_FOUND,
            );
          }
        }
        break;
      case JobReportTypeEnum.INTERNAL:
        if (payload.invoiceContact) {
          throw new BadRequestException(
            JOB_KEYS.REPORT_TYPE_INTERNAL_DOES_NOT_HAVE_INVOICE_CONTACT,
          );
        }

        if (payload.rtContacts?.length > 0) {
          if (payload.rtContacts.length > 1) {
            throw new BadRequestException(
              JOB_KEYS.REPORT_TYPE_INTERNAL_HAVE_ONE_DEFAULT_REPORT_TO_CONTACT,
            );
          }

          countRtContacts = await this.contactModel.countDocuments({
            _id: payload.rtContacts[0],
            isInternal: true,
          });

          if (countRtContacts !== payload.rtContacts.length) {
            throw new BadRequestException(
              JOB_KEYS.REPORT_TO_CONTACTS_NOT_FOUND,
            );
          }
        }

        break;
    }
  }

  private async validateUserUpdateJob(user: string, job: any) {
    const jobEmployee = await this.jobEmployeeModel
      .findOne({ job: job._id, employee: user })
      .lean();

    //Co-Worker cannot update job
    if (jobEmployee && job.assignee != user) {
      throw new BadRequestException(JOB_KEYS.CO_WORKER_CANNOT_UPDATE_JOB);
    }
  }

  private async updateEmployeesAndEquipmentsOfJob(
    employees: any[],
    equipments: any[],
    job: any,
    status: JobStatusEnum,
  ) {
    const isMultipleDayJob = job.type === JobPeriodTypeEnum.MULTIPLE_DAYS;
    switch (status) {
      case JobStatusEnum.OPEN:
      case JobStatusEnum.IN_PROGRESS: {
        if (employees && employees.length > 0) {
          const jobEmployees = employees.map((employee) => {
            return {
              ...employee,
              job: job._id,
              plannedDate: isMultipleDayJob
                ? dayjs(employee.plannedDate).utc().startOf('day')
                : dayjs(job.plannedDate).utc().startOf('day'),
            };
          });

          await this.jobEmployeeModel.deleteMany({ job: job._id });
          await this.jobEmployeeModel.insertMany(jobEmployees);
        }

        if (equipments && isArray(equipments)) {
          await this.jobEquipmentModel.deleteMany({ job: job._id });
          if (equipments.length > 0) {
            const jobEquipments = equipments.map((equipment) => {
              return {
                ...equipment,
                plannedDate: isMultipleDayJob
                  ? dayjs(equipment.plannedDate).utc().startOf('day')
                  : dayjs(job.plannedDate).utc().startOf('day'),
                job: job._id,
              };
            });
            await this.jobEquipmentModel.insertMany(jobEquipments);
          }
        }
        break;
      }
      case JobStatusEnum.READY: {
        if (employees && employees.length > 0) {
          const jobEmployees = employees.map((employee) => {
            return {
              ...employee,
              job: job._id,
              plannedDate: isMultipleDayJob
                ? dayjs(employee.plannedDate).utc().startOf('day')
                : dayjs(job.plannedDate).utc().startOf('day'),
              actualHours: employee.actualHours || 0,
            };
          });

          await this.jobEmployeeModel.bulkWrite(
            jobEmployees.map((jobEmployee) => ({
              updateOne: {
                filter: {
                  job: job._id,
                  employee: jobEmployee.employee,
                  plannedDate: jobEmployee.plannedDate,
                },
                update: { $set: { actualHours: jobEmployee.actualHours } },
              },
            })),
          );
        }
        break;
      }
      default:
        break;
    }
  }

  async exportJobDetail(payload: any) {
    const { id: jobId, user } = payload;

    const job = await this.jobModel
      .findById(jobId)
      .select('units location jobType')
      .populate('location units')
      .populate({ path: 'location', populate: [{ path: 'address' }] })
      .populate({ path: 'units', populate: [{ path: 'parent' }] })
      .lean();

    if (!job) {
      throw new NotFoundException(JOB_KEYS.NOT_FOUND);
    }

    await this.ableDownloadCsvFile(user, job.jobType);

    const reserved: any =
      (await firstValueFrom(
        this.inventoryClient.send(
          { cmd: RESERVED_MESSAGES.FIND_ONE_RESERVED },
          {
            id: jobId.toString(),
            user: payload.user,
          },
        ),
      )) || {};

    const reservedDetails = reserved ? reserved.reservedDetails || [] : [];

    const units = job.units as UnitModel[];
    const location = job.location as LocationModel;
    const address = location.address as AddressModel;

    const fullAdressFileName = `${address.city}-${address.number}${
      address.suffix ? `-${address.suffix}` : ''
    }`;

    let totalOccupants = 0;

    const locationUnit = await this.unitModel
      .findOne({
        name: location.fullAddress,
      })
      .lean();

    const unitsWithJobPoints: (UnitModel & { points: any[] })[] =
      await Promise.all(
        map(units, async (unit) => {
          const points = (await this.jobPointModel
            .find({
              job: job._id,
              unit: (unit as Ref<UnitModel>)._id,
            })
            .lean()) as JobPointModel[];

          return {
            ...unit,
            points,
          };
        }),
      );

    if (locationUnit) {
      totalOccupants = locationUnit.maxOccupants || 0;
    }

    const unitsToListing = unitsWithJobPoints.reduce(
      (cur: any[], unit: UnitModel) => {
        const unitParent = unit.parent as UnitModel;

        let unitName = unitParent?.name
          ? `${unitParent.name}/${unit.name}`
          : unit.name;
        const parentIsRootUnit =
          unitParent?.name &&
          unitParent.name.trim() === location.fullAddress.trim();

        const area = Number(unit.maxArea) || 0;
        const maxOccupants = Number(unit.maxOccupants) || 0;

        const isLocationUnit = unit.name === location.fullAddress;

        if (isLocationUnit) {
          totalOccupants = unit.maxOccupants || locationUnit?.maxOccupants || 0;
        }

        if (parentIsRootUnit) {
          unitName = unit.name;
        }

        cur.push({
          ...unit,
          name: unitName,
          position: unit.position,
          unitName: unit.name,
          maxOccupants,
          area: area ? formatNumberNL(area) : 0,
        });

        return cur;
      },
      [],
    );

    const maxColumnLength = 6;
    const gap = fillCSVContent([], maxColumnLength);

    const stringUnits = unitsToListing
      .map(({ name, area, points }) => {
        const pointStr = points
          .map((item: any) =>
            fillCSVContent([item.description], maxColumnLength),
          )
          .join('');

        return fillCSVContent([name, `${area}m2`], maxColumnLength) + pointStr;
      })
      .join('');

    const titleStr = fillCSVContent(['Job'], maxColumnLength);

    const locationInfoStr = [
      fillCSVContent(['Woning:', address.city], maxColumnLength),
      fillCSVContent(['Adres:', location.fullAddress], maxColumnLength),
      fillCSVContent(['Aantal personen:', totalOccupants], maxColumnLength),
    ].join('');

    const headerUnitStr = fillCSVContent(
      [
        'Unit/Sub-unit',
        'Kenmerken / afmetingen',
        'To do',
        'Kosten arbeid',
        'Kosten material',
        'Totale kosten',
      ],
      maxColumnLength,
    );

    const headerReservedStr = fillCSVContent(
      ['Artikellijst', 'Benodigde artikelen', 'Opgehaald'],
      maxColumnLength,
    );

    const stringReserved = reservedDetails
      .map(({ article, amount, pickedUp }) =>
        fillCSVContent(
          [`${article.identifier} - ${article.description}`, amount, pickedUp],
          maxColumnLength,
        ),
      )
      .join('');

    const data =
      titleStr +
      locationInfoStr +
      gap +
      headerUnitStr +
      stringUnits +
      gap +
      gap +
      headerReservedStr +
      stringReserved;

    const fileName = `job-${fullAdressFileName.toLowerCase()}.csv`;

    return {
      fileName,
      content: data,
    };
  }

  private async sendEmailWhenCompletingJob(
    jobId: string,
    tenantId: string,
    pdfPublicUrl: string,
    residentPdfPublicUrl: string,
    isSendRC: boolean,
    isSendRR: boolean,
  ) {
    this.logger.log('Prepare to send mail when comleting job');
    const job = await this.jobModel.findById(jobId).populate([
      {
        path: 'rtContacts',
        select: '_id email',
      },
      {
        path: 'rrContacts',
        select: '_id displayName email',
      },
    ]);
    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    if (!job.rtContacts) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const companyInfomation =
      await this.tenantService.getCompanyInfomation(tenantId);

    const emailCompleteJob = await this.emailTemplateModel.findOne({
      name: 'job_complete',
    });

    if (!emailCompleteJob?.html) {
      throw new BadRequestException(JOB_KEYS.EMAIL_TEMPLATE_NOT_FOUND);
    }

    const html = emailCompleteJob.html;

    const compiledObject = template(html);
    const data = {
      JOB_ID: job.identifier,
      FULLADDRESS: job.locationInfo?.fullAddress,
      SIGNATURE_EMAIL: companyInfomation['email'],
      COMPANY_SIGN: companyInfomation['sign'],
      COMPANY_BANNER: companyInfomation['banner'],
      COMPANY_TYPOGRAPHY_COLORS_PRIMARY:
        companyInfomation['typography']['colors']['primary'],
      COMPANY_TELEPHONE: companyInfomation['telephone'],
      COMPANY_ADDRESS1: companyInfomation['address1'],
      COMPANY_ADDRESS2: companyInfomation['address2'],
      COMPANY_WEBSITE: companyInfomation['website'],
      COMPANY_LOGO: companyInfomation['logo'],
      COMPANY_LINKEDIN_URL: companyInfomation['linkedinUrl'],
      COMPANY_LINKEDIN_LOGO: companyInfomation['linkedinLogo'],
      COMPANY_SFN_LOGO: companyInfomation['sfnLogo'],
    };

    if (isSendRC) {
      data['LINK'] = pdfPublicUrl;
      const htmlTemplate = compiledObject(data);
      const listEmail: string[] = [];
      job.rtContacts.forEach((rtContact: any) => {
        if (rtContact.email && rtContact.email !== '') {
          listEmail.push(rtContact.email);
        }
      });

      await this.emailService.sendEmail({
        html: htmlTemplate,
        text: htmlTemplate,
        to: listEmail,
        subject: `${job.identifier} ${job.title}`,
        bcc: emailCompleteJob.bcc,
        cc: emailCompleteJob.cc,
      });
    }

    if (isSendRR && job.rrContacts) {
      data['LINK'] = residentPdfPublicUrl;
      const htmlTemplate = compiledObject(data);
      const listEmail: string[] = [];
      job.rrContacts.forEach((rrContact: any) => {
        if (rrContact.email && rrContact.email !== '') {
          listEmail.push(`${rrContact.displayName} <${rrContact.email}>`);
        }
      });

      await this.emailService.sendEmail({
        html: htmlTemplate,
        text: htmlTemplate,
        to: listEmail,
        subject: `${job.identifier} ${job.title}`,
        bcc: emailCompleteJob.bcc,
        cc: emailCompleteJob.cc,
      });
    }
  }

  private async sendEmailWhenRejectingJob(
    jobId: string,
    tenantId: string,
    reason: string,
  ) {
    this.logger.log('Prepare to send mail when rejecting job');
    const job = await this.jobModel
      .findById(jobId)
      .populate([
        {
          path: 'assignee',
          select: '_id firstName',
        },
      ])
      .lean();
    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const emailRejectJob = await this.emailTemplateModel.findOne({
      name: 'job_reject',
    });

    if (!emailRejectJob?.html) {
      throw new BadRequestException(JOB_KEYS.EMAIL_TEMPLATE_NOT_FOUND);
    }

    const html = emailRejectJob.html;

    const compiledObject = template(html);

    const data = {
      ASSIGNEE: (job.assignee as any)?.firstName,
      JOB_ID: job.identifier,
      FULLADDRESS: job.locationInfo?.fullAddress,
      REASON: reason,
    };
    const companyInfomation =
      await this.tenantService.getCompanyInfomation(tenantId);

    const htmlTemplate = compiledObject(data);
    const assigneeEmail = job.assigneeInfo?.email;
    if (assigneeEmail) {
      const listEmail = [assigneeEmail];
      let subject = `${job.identifier} ${job.title}`;

      switch (job.jobType) {
        case JobTypeEnum.INSPECTION:
          subject = `${companyInfomation['brandName']} - inspection afgewezen`;
          break;
        case JobTypeEnum.MAINTENANCE:
          subject = `${companyInfomation['brandName']} - Onderhoudstaak afgewezen`;
          break;
        case JobTypeEnum.CLEANING:
          subject = `${companyInfomation['brandName']} - Schoonmaaktaak afgewezen`;
          break;
      }

      await this.emailService.sendEmail({
        html: htmlTemplate,
        text: htmlTemplate,
        to: listEmail,
        subject: subject,
        bcc: emailRejectJob.bcc,
        cc: emailRejectJob.cc,
      });
    }
  }

  private async sendEmailWhenAssigneeJob(jobId: string, tenantId: string) {
    this.logger.log('Prepare to send mail when assigne job');
    const job = await this.jobModel
      .findById(jobId)
      .populate([
        {
          path: 'assignee',
          select: '_id firstName',
        },
      ])
      .lean();
    if (!job) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    const emailRejectJob = await this.emailTemplateModel.findOne({
      name: 'job_assigne',
    });

    if (!emailRejectJob?.html) {
      throw new BadRequestException(JOB_KEYS.EMAIL_TEMPLATE_NOT_FOUND);
    }

    const html = emailRejectJob.html;

    const compiledObject = template(html);

    const data = {
      ASSIGNEE: (job.assignee as any)?.firstName,
      JOB_ID: job.identifier,
      FULLADDRESS: job.locationInfo?.fullAddress,
      PLANNED_DATE: dayjs(job.plannedDate).utc().format(DATE_FORMAT_SLASH),
      INSTRUCTIONS: job.instructions,
    };
    const companyInfomation =
      await this.tenantService.getCompanyInfomation(tenantId);

    const htmlTemplate = compiledObject(data);
    const assigneeEmail = job.assigneeInfo?.email;
    if (assigneeEmail) {
      const listEmail = [assigneeEmail];
      let subject = `${job.identifier} ${job.title}`;

      switch (job.jobType) {
        case JobTypeEnum.INSPECTION:
          subject = `${companyInfomation['brandName']} - Inspectie toegewezen`;
          break;
        case JobTypeEnum.MAINTENANCE:
          subject = `${companyInfomation['brandName']} - Onderhoudstaak toegewezen`;
          break;
        case JobTypeEnum.CLEANING:
          subject = `${companyInfomation['brandName']} - Schoonmaaktaak toegewezen`;
          break;
      }

      await this.emailService.sendEmail({
        html: htmlTemplate,
        text: htmlTemplate,
        to: listEmail,
        subject: subject,
        bcc: emailRejectJob.bcc,
        cc: emailRejectJob.cc,
      });
    }
  }

  private async validatePermissionOfUser(
    userId: string,
    roles: TenantRoleEnum[],
  ) {
    const isValid = await this.tenantUserService.verifyRoles(userId, roles);

    return isValid;
  }

  private async validatePermissionCreateJob(
    user: string,
    jobType: JobTypeEnum,
    platform: string,
  ) {
    let isValid = false;
    switch (jobType) {
      case JobTypeEnum.INSPECTION:
        isValid = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.INSPECTION_PLANNER,
        ]);
        break;
      case JobTypeEnum.MAINTENANCE:
        if (platform === 'mb') {
          isValid = await this.validatePermissionOfUser(user, [
            TenantRoleEnum.MECHANIC,
          ]);
        } else {
          isValid = await this.validatePermissionOfUser(user, [
            TenantRoleEnum.MAINTENANCE_PLANNER,
          ]);
        }

        break;
      case JobTypeEnum.CLEANING:
        if (platform === 'mb') {
          isValid = await this.validatePermissionOfUser(user, [
            TenantRoleEnum.CLEANER,
          ]);
        } else {
          isValid = await this.validatePermissionOfUser(user, [
            TenantRoleEnum.CLEANING_PLANNER,
          ]);
        }
        break;
    }

    if (!isValid) {
      throw new ForbiddenException();
    }
  }

  private async getAllowedJobStatusForGetAllJob(
    user: string,
    jobType: JobTypeEnum | undefined,
  ) {
    const jobStatus: JobStatusQueryEnum[] = [];
    let isPlanner = false;
    let isReviewer = false;
    let isViewer = false;

    switch (jobType) {
      case JobTypeEnum.INSPECTION:
        isPlanner = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.INSPECTION_PLANNER,
        ]);
        isReviewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.INSPECTION_REVIEWER_ALL_INSPECTIONS,
        ]);
        isViewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.INSPECTION_VIEWER,
        ]);
        break;
      case JobTypeEnum.MAINTENANCE:
        isPlanner = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.MAINTENANCE_PLANNER,
        ]);
        isReviewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.MAINTENANCE_REVIEWER,
        ]);
        isViewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.MAINTENANCE_VIEWER,
        ]);
        break;
      case JobTypeEnum.CLEANING:
        isPlanner = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.CLEANING_PLANNER,
        ]);
        isReviewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.CLEANING_REVIEWER,
        ]);
        isViewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.CLEANING_VIEWER,
        ]);
        break;
    }

    if (isPlanner) {
      jobStatus.push(
        ...[JobStatusQueryEnum.OPEN, JobStatusQueryEnum.IN_PROGRESS],
      );
    }

    if (isReviewer) {
      const allStatus = Object.values(JobStatusQueryEnum) as [
        JobStatusQueryEnum,
        ...JobStatusQueryEnum[],
      ];
      jobStatus.push(...allStatus);
    }

    if (isViewer) {
      jobStatus.push(
        ...[JobStatusQueryEnum.COMPLETED, JobStatusQueryEnum.CLOSED],
      );
    }

    return jobStatus.filter(
      (item, index, self) => self.indexOf(item) === index,
    );
  }

  private async ableDownloadCsvFile(user, jobType) {
    let ableDownloadCsvFile = false;
    switch (jobType) {
      case JobTypeEnum.INSPECTION:
        ableDownloadCsvFile = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.INSPECTION_PLANNER,
          TenantRoleEnum.INSPECTION_VIEWER,
          TenantRoleEnum.INSPECTION_REVIEWER_ALL_INSPECTIONS,
        ]);
        break;
      case JobTypeEnum.MAINTENANCE:
        ableDownloadCsvFile = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.MAINTENANCE_PLANNER,
          TenantRoleEnum.MAINTENANCE_VIEWER,
          TenantRoleEnum.MAINTENANCE_REVIEWER,
        ]);
        break;
      case JobTypeEnum.CLEANING:
        ableDownloadCsvFile = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.CLEANING_PLANNER,
          TenantRoleEnum.CLEANING_VIEWER,
          TenantRoleEnum.CLEANING_REVIEWER,
        ]);
        break;
    }

    if (!ableDownloadCsvFile) {
      throw new ForbiddenException();
    }
  }

  private async getAllowedJobStatusForDeleteJob(
    user: string,
    jobType: JobTypeEnum,
  ) {
    const jobStatus: JobStatusQueryEnum[] = [];
    let isPlanner = false;
    let isReviewer = false;

    switch (jobType) {
      case JobTypeEnum.INSPECTION:
        isPlanner = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.INSPECTION_PLANNER,
        ]);
        isReviewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.INSPECTION_REVIEWER_ALL_INSPECTIONS,
        ]);
        break;
      case JobTypeEnum.MAINTENANCE:
        isPlanner = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.MAINTENANCE_PLANNER,
        ]);
        isReviewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.MAINTENANCE_REVIEWER,
        ]);
        break;
      case JobTypeEnum.CLEANING:
        isPlanner = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.CLEANING_PLANNER,
        ]);
        isReviewer = await this.validatePermissionOfUser(user, [
          TenantRoleEnum.CLEANING_REVIEWER,
        ]);
        break;
    }

    if (isPlanner) {
      jobStatus.push(...[JobStatusQueryEnum.OPEN]);
    }

    if (isReviewer) {
      jobStatus.push(...[JobStatusQueryEnum.OPEN, JobStatusQueryEnum.CLOSED]);
    }

    return jobStatus.filter(
      (item, index, self) => self.indexOf(item) === index,
    );
  }

  private async updateCostLineWhenCompleteJob(jobId) {
    const foundJob = await this.jobModel.findById(jobId).lean();
    if (!foundJob) {
      throw new BadRequestException(JOB_KEYS.NOT_FOUND);
    }

    let isHasCostLines = false;

    if (
      foundJob.reportType === JobReportTypeEnum.CUSTOMER ||
      !foundJob.reportType
    ) {
      const invoiceContact = foundJob.invoiceContact;
      const jobPoints =
        (await this.jobPointModel
          .find({
            job: jobId,
          })
          .lean()) || [];
      const costLineOfJobPoints = flatMap(jobPoints, (jobPoint) =>
        jobPoint.costLines && jobPoint.costLines.length > 0
          ? jobPoint.costLines.map((v: any) => v.toString())
          : [],
      );

      isHasCostLines = costLineOfJobPoints.length > 0;

      if (isHasCostLines) {
        await this.costLineModel
          .updateMany(
            {
              _id: {
                $in: costLineOfJobPoints,
              },
            },
            {
              contact: invoiceContact,
            },
          )
          .exec();
      }
    }

    return isHasCostLines;
  }

  //#endregion
}
