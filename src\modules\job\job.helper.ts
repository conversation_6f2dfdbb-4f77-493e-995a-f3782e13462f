import { BadRequestException } from '@nestjs/common';
import dayjs, { Dayjs, ManipulateType } from 'dayjs';
import { Types } from 'mongoose';
import { ZodError } from 'zod';

import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import {
  JobPeriodTypeEnum,
  JobPointStatusEnum,
  JobStatusEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';
import { JOB_KEYS } from '~/shared/message-keys/job.message-keys';
import { isValidImageExtension } from '~/utils/image.util';

import { JobPointAction } from '../job-point/job-point.model';
import {
  MobilePerformJobSchema,
  MobileUpdateInProgressJobSchema,
  RejectJobSchema,
  UpdateInProgressJobSchema,
  UpdateInProgressMultipleDaysJobSchema,
  UpdateOpenJobSchema,
  UpdateOpenMultipleDaysJobSchema,
  UpdateReviewJobSchema,
  UpdateReviewMultipleDaysJobSchema,
  UpdateScheduleJobSchema,
} from './dtos/job.dto';

export const MONTH_GENERATE = 3;

export enum TypeRule {
  DAYS = 'days',
  WEEKS = 'weeks',
  MONTHS = 'months',
  YEARS = 'years',
}

export const TypeRuleDayJS = {
  daily: 'days',
  weekly: 'weeks',
  monthly: 'months',
  yearly: 'years',
};

const dayNameToIndex: { [key: string]: number } = {
  MO: 1,
  TU: 2,
  WE: 3,
  TH: 4,
  FR: 5,
  SA: 6,
  SU: 0,
};

export const projectPipelineStage = {
  $project: {
    isActive: 1,
    fIdentifier: 1,
    fStartDate: 1,
    fEndDate: 1,
    identifier: 1,
    title: 1,
    status: 1,
    type: 1,
    plannedDate: 1,
    plannedEndDate: 1,
    assignee: '$assigneeInfo',
    location: {
      _id: '$locationInfo._id',
      fullAddress: '$locationInfo.fullAddress',
      team: {
        _id: '$locationTeamInfo._id',
        name: '$locationTeamInfo.name',
      },
    },
    units: 1,
    createdAt: 1,
    updatedAt: 1,
  },
};

export const projectPipelineStageMobile = {
  $project: {
    isActive: 1,
    identifier: 1,
    status: 1,
    jobType: 1,
    type: 1,
    plannedDate: 1,
    plannedEndDate: 1,
    location: {
      _id: '$locationInfo._id',
      fullAddress: '$locationInfo.fullAddress',
      team: {
        _id: '$locationTeamInfo._id',
        name: '$locationTeamInfo.name',
      },
    },
    createdAt: 1,
    updatedAt: 1,
  },
};

const portalProjectFieldDetailJob = [
  {
    $project: {
      _id: 1,
      isActive: 1,
      status: 1,
      identifier: 1,
      title: 1,
      locationInfo: 1,
      plannerInfo: 1,
      plannedDate: 1,
      plannedEndDate: 1,
      assigneeInfo: 1,
      instructions: 1,
      units: 1,
      images: 1,
      reportType: 1,
      reportContact: 1,
      type: 1,
      jobType: 1,
      pdfPublicUrl: 1,
      feedbacks: 1,
      fuaDescriptions: 1,
      fInterval: 1,
      fRule: 1,
      fDays: 1,
      fDayInMonth: 1,
      fStartDate: 1,
      fEndDate: 1,
      isOverdue: 1,
      jobPoints: 1,
      employees: 1,
      equipments: 1,
    },
  },
];

const mobileProjectFieldDetailJob = [
  {
    $project: {
      _id: 1,
      isActive: 1,
      status: 1,
      identifier: 1,
      title: 1,
      location: 1,
      plannerInfo: 1,
      plannedDate: 1,
      plannedEndDate: 1,
      assigneeInfo: 1,
      instructions: 1,
      units: 1,
      images: 1,
      reportType: 1,
      reportContact: 1,
      type: 1,
      jobType: 1,
      feedbacks: 1,
      isOverdue: 1,
      jobPoints: 1,
      employees: 1,
      equipments: 1,
    },
  },
];

export const mobileGetAllJobsSelectedFields = {
  _id: 1,
  isActive: 1,
  isDeleted: 1,
  identifier: 1,
  title: 1,
  status: 1,
  type: 1,
  jobType: 1,
  plannedDate: 1,
  plannedEndDate: 1,
  location: '$locationInfo',
  assignee: 1,
};

export const baseAggregateOfDetailJob = [
  {
    $lookup: {
      from: 'units',
      localField: 'units',
      foreignField: '_id',
      as: 'units',
      pipeline: [
        {
          $project: {
            _id: 1,
            name: 1,
            parent: 1,
          },
        },
      ],
    },
  },
  {
    $lookup: {
      from: 'contacts',
      localField: 'reportContact',
      foreignField: '_id',
      as: 'reportContact',
      pipeline: [
        {
          $project: {
            _id: 1,
            contactType: 1,
            contactRole: 1,
            displayName: 1,
          },
        },
      ],
    },
  },
  {
    $set: {
      reportContact: { $arrayElemAt: ['$reportContact', 0] },
    },
  },
  {
    $lookup: {
      from: 'jobemployees',
      localField: '_id',
      foreignField: 'job',
      as: 'employees',
      pipeline: [
        {
          $lookup: {
            from: 'tenantusers',
            localField: 'employee',
            foreignField: '_id',
            as: 'employee',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  displayName: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            employee: { $arrayElemAt: ['$employee', 0] },
          },
        },
        {
          $project: {
            employee: 1,
            estimatedHours: 1,
            actualHours: 1,
            plannedDate: 1,
          },
        },
      ],
    },
  },
  {
    $lookup: {
      from: 'jobpoints',
      let: { jobId: '$_id', jobStatus: '$status' },
      as: 'jobPoints',
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ['$job', '$$jobId'] },
                { $eq: ['$$jobStatus', JobStatusEnum.OPEN] },
              ],
            },
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: 'unit',
            foreignField: '_id',
            as: 'unit',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  name: 1,
                  parent: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            unit: { $arrayElemAt: ['$unit', 0] },
          },
        },
        {
          $project: {
            _id: 1,
            description: 1,
            position: 1,
            unit: 1,
          },
        },
      ],
    },
  },
  {
    $lookup: {
      from: 'jobequipments',
      localField: '_id',
      foreignField: 'job',
      as: 'equipments',
      pipeline: [
        {
          $lookup: {
            from: 'equipment',
            localField: 'equipment',
            foreignField: '_id',
            as: 'equipment',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  description: 1,
                  type: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            equipment: { $arrayElemAt: ['$equipment', 0] },
          },
        },
        {
          $project: {
            equipment: 1,
            plannedDate: 1,
          },
        },
      ],
    },
  },
];

export const mobileAggregateOfDetailJob = [
  ...baseAggregateOfDetailJob,
  {
    $lookup: {
      from: 'locations',
      localField: 'location',
      foreignField: '_id',
      as: 'location',
      pipeline: [
        {
          $project: {
            _id: 1,
            fullAddress: 1,
            locationOf: 1,
          },
        },
      ],
    },
  },
  {
    $set: {
      location: { $arrayElemAt: ['$location', 0] },
    },
  },
  ...mobileProjectFieldDetailJob,
];

export const portalAggregateOfDetailJob = [
  ...baseAggregateOfDetailJob,
  ...portalProjectFieldDetailJob,
];

export const aggregateOfViewSummaryOfJob = (
  id: string,
  platform: string,
  user: string,
) => {
  let queryEmployee = {};
  if (platform === 'mb') {
    queryEmployee = {
      employee: new Types.ObjectId(user),
    };
  }
  return [
    {
      $match: {
        _id: new Types.ObjectId(id),
      },
    },
    {
      $lookup: {
        from: 'jobequipments',
        localField: '_id',
        foreignField: 'job',
        as: 'equipments',
        pipeline: [
          {
            $lookup: {
              from: 'equipment',
              localField: 'equipment',
              foreignField: '_id',
              as: 'equipment',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    description: 1,
                    type: 1,
                  },
                },
              ],
            },
          },
          {
            $set: {
              equipment: { $arrayElemAt: ['$equipment', 0] },
            },
          },
          {
            $project: {
              equipment: 1,
              plannedDate: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'jobemployees',
        localField: '_id',
        foreignField: 'job',
        as: 'employees',
        pipeline: [
          {
            $match: {
              ...queryEmployee,
            },
          },
          {
            $lookup: {
              from: 'tenantusers',
              localField: 'employee',
              foreignField: '_id',
              as: 'employee',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    displayName: 1,
                  },
                },
              ],
            },
          },
          {
            $set: {
              employee: { $arrayElemAt: ['$employee', 0] },
            },
          },
          {
            $project: {
              employee: 1,
              estimatedHours: 1,
              actualHours: 1,
              plannedDate: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'jobpoints',
        localField: '_id',
        foreignField: 'job',
        as: 'jobPoints',
        pipeline: [
          {
            $lookup: {
              from: 'units',
              localField: 'unit',
              foreignField: '_id',
              as: 'unit',
              pipeline: [
                {
                  $lookup: {
                    from: 'units',
                    localField: 'parent',
                    foreignField: '_id',
                    as: 'parent',
                    pipeline: [
                      {
                        $project: {
                          _id: 1,
                          name: 1,
                          position: 1,
                        },
                      },
                    ],
                  },
                },
                {
                  $set: {
                    parent: { $arrayElemAt: ['$parent', 0] },
                  },
                },
                {
                  $project: {
                    _id: 1,
                    name: 1,
                    parent: 1,
                  },
                },
              ],
            },
          },
          {
            $set: {
              unit: { $arrayElemAt: ['$unit', 0] },
            },
          },
          {
            $project: {
              _id: 1,
              status: 1,
              description: 1,
              position: 1,
              unit: 1,
              action: 1,
              costlines: 1,
              note: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'units',
        localField: 'units',
        foreignField: '_id',
        as: 'units',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              parent: 1,
              position: 1,
              isRoot: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'contacts',
        localField: 'reportContact',
        foreignField: '_id',
        as: 'reportContact',
        pipeline: [
          {
            $project: {
              _id: 1,
              contactType: 1,
              contactRole: 1,
              displayName: 1,
            },
          },
        ],
      },
    },
    {
      $set: {
        reportContact: { $arrayElemAt: ['$reportContact', 0] },
      },
    },
    {
      $lookup: {
        from: 'contacts',
        localField: 'rtContacts',
        foreignField: '_id',
        as: 'rtContacts',
        pipeline: [
          {
            $project: {
              _id: 1,
              contactType: 1,
              contactRole: 1,
              displayName: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'nightregistrationresidents',
        localField: 'rrContacts',
        foreignField: '_id',
        as: 'rrContacts',
        pipeline: [
          {
            $project: {
              _id: 1,
              displayName: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'contacts',
        localField: 'invoiceContact',
        foreignField: '_id',
        as: 'invoiceContact',
        pipeline: [
          {
            $project: {
              _id: 1,
              contactType: 1,
              contactRole: 1,
              displayName: 1,
            },
          },
        ],
      },
    },
    {
      $set: {
        invoiceContact: { $arrayElemAt: ['$invoiceContact', 0] },
      },
    },
    {
      $project: {
        _id: 1,
        identifier: 1,
        equipments: 1,
        title: 1,
        status: 1,
        type: 1,
        jobType: 1,
        units: 1,
        reportType: 1,
        reportContact: 1,
        isSendRC: 1,
        isSendRR: 1,
        rtContacts: 1,
        rrContacts: 1,
        invoiceContact: 1,
        location: 1,
        employees: 1,
        jobPoints: 1,
      },
    },
  ];
};

function convertDayNameToIndex(dayName: string): number | undefined {
  return dayNameToIndex[dayName];
}

export function getRangeOfDates({
  start,
  end,
  key,
  offset = 1,
  arr = [start.startOf('day').toISOString()],
}: {
  start: Dayjs;
  end: Dayjs;
  key: ManipulateType;
  offset: number;
  arr?: string[];
}): string[] {
  const next = dayjs(start).add(offset, key).startOf('day');

  if (next.isAfter(end)) return arr;

  return getRangeOfDates({
    start: next,
    end,
    key,
    offset,
    arr: arr.concat(next.toISOString()),
  });
}

export function getDateOfWeek(date: string, dayOfWeeks: string[]): string[] {
  const numberDayOfWeeks = dayOfWeeks.map(
    (date) => convertDayNameToIndex(date)!,
  );
  return numberDayOfWeeks.map((dateOfWeek) => {
    const week = dayjs(date).isoWeek();
    const year = dayjs(date).year();

    return dayjs(date)
      .utc()
      .startOf('day')
      .isoWeekday(dateOfWeek)
      .isoWeek(week)
      .year(year)
      .toISOString();
  });
}

export function getDayInMonth(date: string, dayInMonth: string): string[] {
  const listDayInMonth: string[] = [];
  const numberOfDayInMonth = convertDayNameToIndex(dayInMonth) || 0;

  let currentDay = dayjs(date).utc().startOf('month').day(numberOfDayInMonth);
  if (currentDay.date() > 7) currentDay = currentDay.add(7, 'day');
  const month = currentDay.month();

  while (month === currentDay.month()) {
    listDayInMonth.push(currentDay.toISOString());
    currentDay = currentDay.add(7, 'day');
  }

  return listDayInMonth;
}

export function generateRangeDatesMatchConditions(
  djsStartDate?: Dayjs | null,
  djsEndDate?: Dayjs | null,
) {
  const matchJobConditions: any = {};
  const matchMultipleDaysConditions: any = {};

  if (djsStartDate && djsEndDate) {
    matchJobConditions.$and = [
      {
        plannedDate: {
          $gte: djsStartDate.toDate(),
          $lte: djsEndDate.toDate(),
        },
      },
    ];
    matchMultipleDaysConditions.$and = [
      {
        plannedDate: {
          $gte: djsStartDate.toDate(),
          $lte: djsEndDate.toDate(),
        },
      },
      {
        plannedEndDate: {
          $gte: djsStartDate.toDate(),
          $lte: djsEndDate.toDate(),
        },
      },
    ];
  } else if (djsStartDate) {
    matchJobConditions.$and = [
      {
        plannedDate: {
          $gte: djsStartDate.toDate(),
        },
      },
    ];

    matchMultipleDaysConditions.$and = [
      {
        plannedDate: {
          $gte: djsStartDate.toDate(),
        },
      },
      {
        plannedEndDate: {
          $gte: djsStartDate.toDate(),
        },
      },
    ];
  } else if (djsEndDate) {
    matchJobConditions.$and = [
      {
        plannedDate: {
          $lte: djsEndDate.toDate(),
        },
      },
    ];

    matchMultipleDaysConditions.$and = [
      {
        plannedDate: {
          $lte: djsEndDate.toDate(),
        },
      },
      {
        plannedEndDate: {
          $lte: djsEndDate.toDate(),
        },
      },
    ];
  }
  matchJobConditions.$and = matchJobConditions.$and || [];
  matchJobConditions.$and.push({
    plannedEndDate: {
      $exists: false,
    },
  });
  matchMultipleDaysConditions.$and = matchMultipleDaysConditions.$and || [];

  const matchConditions = {
    $or: [matchJobConditions, matchMultipleDaysConditions],
  };

  return matchConditions;
}

export function getJobStatus(status: JobStatusEnum) {
  switch (status) {
    case JobStatusEnum.COMPLETE:
      return 'completed';
    case JobStatusEnum.CLOSE:
      return 'closed';
    case JobStatusEnum.REJECT:
      return JobStatusEnum.OPEN;
    default:
      return status;
  }
}

export function buildEmployeesSchedulesAggregate(
  period: {
    startDate: Date;
    endDate: Date;
    timezone: string;
  },
  employees: Types.ObjectId[],
  location?: string,
): any[] {
  const { startDate, endDate, timezone } = period;

  const matchNonPeriodicActiveJobWithinPeriod = {
    $match: {
      $or: [
        {
          plannedDate: { $gte: startDate, $lte: endDate },
          plannedEndDate: { $eq: null },
        },
        { plannedDate: { $lte: endDate }, plannedEndDate: { $gte: startDate } },
      ],
      isActive: true,
      isDeleted: false,
      type: {
        $in: Object.values(JobPeriodTypeEnum).filter(
          (v) => v !== JobPeriodTypeEnum.PERIODIC,
        ),
      },
    },
  };
  const findAndPopulateLocation = {
    $lookup: {
      from: 'locations',
      localField: 'location',
      foreignField: '_id',
      as: 'location',
      ...(location && {
        pipeline: [{ $match: { _id: new Types.ObjectId(location) } }],
      }),
    },
  };
  const unwindFoundLocation = {
    $unwind: { path: '$location', preserveNullAndEmptyArrays: false },
  };
  const findAndPopulateJobEmployees = {
    $lookup: {
      from: 'jobemployees',
      localField: '_id',
      foreignField: 'job',
      as: 'jobemployee',
      pipeline: [
        {
          $match: {
            employee: { $in: employees },
            plannedDate: { $gte: startDate, $lte: endDate },
          },
        },
      ],
    },
  };
  const unwindFoundJobEmployees = {
    $unwind: {
      path: '$jobemployee',
      preserveNullAndEmptyArrays: false,
    },
  };
  const populateEmployeeFromJobEmployees = {
    $lookup: {
      from: 'tenantusers',
      localField: 'jobemployee.employee',
      foreignField: '_id',
      as: 'jobemployee.employee',
      pipeline: [{ $match: { isActive: true, isDeleted: false } }],
    },
  };
  const takeTheFirstEmployee = {
    $addFields: { 'jobemployee.employee': { $first: '$jobemployee.employee' } },
  };
  const removeNullEmployee = {
    $match: { 'jobemployee.employee._id': { $exists: true } },
  };
  const getEstimatedHoursFromJobEmployee = {
    $addFields: {
      estimatedHours: '$jobemployee.estimatedHours',
    },
  };
  const getDayFromPlannedDate = {
    $addFields: {
      date: {
        $dateToString: {
          date: '$jobemployee.plannedDate',
          format: '%Y-%m-%d',
          timezone,
        },
      },
    },
  };
  const groupJobsByEmployeeAndDate = {
    $group: {
      _id: {
        employee: '$jobemployee.employee._id',
        date: '$date',
      },
      date: { $first: '$date' },
      employee: { $first: '$jobemployee.employee' },
      items: { $push: '$$ROOT' },
    },
  };
  const sortByDateAsc = { $sort: { date: 1 } };
  const groupSchedulesByEmployee = {
    $group: {
      _id: { employee: '$_id.employee' },
      employee: { $first: '$employee' },
      schedules: { $push: '$$ROOT' },
    },
  };
  const projectNeededFieldsOnly = {
    $project: {
      _id: 0,
      employee: {
        _id: 1,
        displayName: 1,
      },
      schedules: {
        date: 1,
        items: {
          _id: 1,
          title: 1,
          type: 1,
          jobType: 1,
          status: 1,
          plannedDate: 1,
          isOverdue: 1,
          location: {
            _id: 1,
            fullAddress: 1,
          },
          estimatedHours: 1,
        },
      },
    },
  };
  const sortByEmployeeNameAsc = { $sort: { 'employee.displayName': 1 } };

  return [
    matchNonPeriodicActiveJobWithinPeriod,
    findAndPopulateLocation,
    unwindFoundLocation,
    findAndPopulateJobEmployees,
    unwindFoundJobEmployees,
    populateEmployeeFromJobEmployees,
    takeTheFirstEmployee,
    removeNullEmployee,
    getEstimatedHoursFromJobEmployee,
    getDayFromPlannedDate,
    groupJobsByEmployeeAndDate,
    sortByDateAsc,
    groupSchedulesByEmployee,
    projectNeededFieldsOnly,
    sortByEmployeeNameAsc,
  ];
}

export function buildEquipmentSchedulesAggregate(
  period: {
    startDate: Date;
    endDate: Date;
    timezone: string;
  },
  equipmentType: EquipmentEnum,
  location?: string,
): any[] {
  const { startDate, endDate, timezone } = period;

  const matchNonPeriodicActiveJobWithinPeriod = {
    $match: {
      $or: [
        {
          plannedDate: { $gte: startDate, $lte: endDate },
          plannedEndDate: { $eq: null },
        },
        { plannedDate: { $lte: endDate }, plannedEndDate: { $gte: startDate } },
      ],
      isActive: true,
      isDeleted: false,
      type: {
        $in: Object.values(JobPeriodTypeEnum).filter(
          (v) => v !== JobPeriodTypeEnum.PERIODIC,
        ),
      },
    },
  };
  const findAndPopulateJobEquipments = {
    $lookup: {
      from: 'jobequipments',
      localField: '_id',
      foreignField: 'job',
      as: 'jobEquipment',
      pipeline: [
        { $match: { plannedDate: { $gte: startDate, $lte: endDate } } },
        { $project: { equipment: 1, plannedDate: 1 } },
        {
          $lookup: {
            from: 'equipments',
            localField: 'equipment',
            foreignField: '_id',
            as: 'equipment',
            pipeline: [
              { $match: { isActive: true, type: equipmentType } },
              { $project: { description: 1, type: 1 } },
            ],
          },
        },
        { $unwind: { path: '$equipment', preserveNullAndEmptyArrays: false } },
      ],
    },
  };
  const unwindFoundJobEquipments = {
    $unwind: { path: '$jobEquipment', preserveNullAndEmptyArrays: false },
  };
  const findAndPopulateLocation = {
    $lookup: {
      from: 'locations',
      localField: 'location',
      foreignField: '_id',
      as: 'location',
      ...(location && {
        pipeline: [{ $match: { _id: new Types.ObjectId(location) } }],
      }),
    },
  };
  const unwindFoundLocation = {
    $unwind: { path: '$location', preserveNullAndEmptyArrays: false },
  };
  const findAndPopulateJobEmployees = {
    $lookup: {
      from: 'jobemployees',
      localField: '_id',
      foreignField: 'job',
      as: 'jobemployee',
      let: { assigneeId: '$assignee' },
      pipeline: [
        {
          $match: {
            $expr: {
              $eq: ['$employee', '$$assigneeId'],
            },
          },
        },
      ],
    },
  };
  const unwindFoundJobEmployees = {
    $unwind: {
      path: '$jobemployee',
      preserveNullAndEmptyArrays: false,
    },
  };
  const getEstimatedHoursFromJobEmployee = {
    $addFields: {
      estimatedHours: '$jobemployee.estimatedHours',
    },
  };
  const getDayFromPlannedDate = {
    $addFields: {
      date: {
        $dateToString: {
          date: '$jobemployee.plannedDate',
          format: '%Y-%m-%d',
          timezone,
        },
      },
    },
  };
  const groupJobsByEquipmentAndDate = {
    $group: {
      _id: {
        equipment: '$jobEquipment.equipment._id',
        date: '$date',
      },
      date: { $first: '$date' },
      equipment: { $first: '$jobEquipment.equipment' },
      items: { $push: '$$ROOT' },
    },
  };
  const sortByDateAsc = { $sort: { date: 1 } };
  const groupSchedulesByEquipment = {
    $group: {
      _id: { equipment: '$_id.equipment' },
      equipment: { $first: '$equipment' },
      schedules: { $push: '$$ROOT' },
    },
  };
  const projectNeededFieldsOnly = {
    $project: {
      _id: 0,
      equipment: {
        _id: 1,
        description: 1,
      },
      schedules: {
        date: 1,
        items: {
          _id: 1,
          title: 1,
          type: 1,
          jobType: 1,
          status: 1,
          plannedDate: 1,
          isOverdue: 1,
          location: {
            _id: 1,
            fullAddress: 1,
          },
          estimatedHours: 1,
        },
      },
    },
  };
  const sortByEquipmentDescriptionAsc = {
    $sort: { 'equipment.description': 1 },
  };

  return [
    matchNonPeriodicActiveJobWithinPeriod,
    findAndPopulateJobEquipments,
    unwindFoundJobEquipments,
    findAndPopulateLocation,
    unwindFoundLocation,
    findAndPopulateJobEmployees,
    unwindFoundJobEmployees,
    getEstimatedHoursFromJobEmployee,
    getDayFromPlannedDate,
    groupJobsByEquipmentAndDate,
    sortByDateAsc,
    groupSchedulesByEquipment,
    projectNeededFieldsOnly,
    sortByEquipmentDescriptionAsc,
  ];
}

function getValidationSchema(
  platform: string,
  status: string,
  isMultipleDaysJob: boolean,
) {
  if (platform === 'mb') {
    const mobileSchemas = {
      [JobStatusEnum.OPEN]: MobilePerformJobSchema,
      [JobStatusEnum.IN_PROGRESS]: MobileUpdateInProgressJobSchema,
    };

    const schema = mobileSchemas[status];
    if (!schema) {
      throw new BadRequestException('Invalid job status flow');
    }

    return schema;
  }

  // For portal platform
  if (status === JobStatusEnum.OPEN) {
    return isMultipleDaysJob
      ? UpdateOpenMultipleDaysJobSchema
      : UpdateOpenJobSchema;
  }

  if (status === JobStatusEnum.IN_PROGRESS) {
    return isMultipleDaysJob
      ? UpdateInProgressMultipleDaysJobSchema
      : UpdateInProgressJobSchema;
  }

  if (status === JobStatusEnum.READY) {
    return isMultipleDaysJob
      ? UpdateReviewMultipleDaysJobSchema
      : UpdateReviewJobSchema;
  }

  if (status === JobStatusEnum.REJECT) {
    return RejectJobSchema;
  }

  throw new BadRequestException('Invalid job status flow');
}

export function validateUpdateData(data: any) {
  try {
    const { platform, status, currentType, ...rest } = data;
    const validatedData = { ...rest };

    if (currentType === JobPeriodTypeEnum.PERIODIC) {
      return UpdateScheduleJobSchema.parse(validatedData);
    }

    const isMultipleDaysJob = currentType === JobPeriodTypeEnum.MULTIPLE_DAYS;
    const schema = getValidationSchema(platform, status, isMultipleDaysJob);

    return schema.parse(validatedData);
  } catch (error: any) {
    if (error instanceof ZodError) {
      const errorPayload = {
        message: 'Validation failed',
        errors: error.errors,
      };
      throw new BadRequestException(errorPayload);
    } else {
      throw error;
    }
  }
}

export function validateUpdateJobFlow({
  currentStatus,
  nextStatus,
  platform = 'portal',
}: {
  currentStatus: JobStatusEnum;
  nextStatus: JobStatusEnum;
  platform: 'mb' | 'portal';
}) {
  const validFlow = {
    portal: {
      [JobStatusEnum.READY]: [JobStatusEnum.COMPLETE, JobStatusEnum.REJECT],
    },
    mb: {
      [JobStatusEnum.OPEN]: [JobStatusEnum.IN_PROGRESS],
      [JobStatusEnum.IN_PROGRESS]: [JobStatusEnum.READY],
    },
  };

  const isValid = validFlow[platform][currentStatus]?.includes(nextStatus);

  if (!isValid) {
    throw new BadRequestException('Invalid job status flow');
  }
}

export function validateImages(imageUrls: string[]) {
  if (!imageUrls) {
    return;
  }

  const isInvalid = imageUrls.some(
    (imageUrl) => !isValidImageExtension(imageUrl),
  );

  if (isInvalid) {
    throw new BadRequestException(JOB_KEYS.INVALID_IMAGES);
  }
}

export function removeUnitsDoesNotHavePoints(units: any[]) {
  const unitsPayload = units.filter(
    (unit) => unit.points && !!unit.points.length,
  );

  if (!unitsPayload.length) {
    throw new BadRequestException(JOB_KEYS.MUST_HAVE_ONE_UNIT_HAVE_POINTS);
  }

  return unitsPayload;
}

export function validatePeriodTypeOfJob(
  type: JobPeriodTypeEnum,
  jobType: JobTypeEnum,
) {
  switch (jobType) {
    case JobTypeEnum.INSPECTION:
      if (type === JobPeriodTypeEnum.NONE) {
        throw new BadRequestException(JOB_KEYS.INSPECTION_JOB_MUST_HAVE_TYPE);
      }
      break;
    case JobTypeEnum.MAINTENANCE:
    case JobTypeEnum.CLEANING:
      if (
        type === JobPeriodTypeEnum.NONE ||
        type === JobPeriodTypeEnum.BEGIN ||
        type === JobPeriodTypeEnum.END
      ) {
        throw new BadRequestException(
          JOB_KEYS.MAINTENANCE_OR_CLEANER_JOB_ONLY_HAVE_TYPE_REGULAR_OR_PERIODIC,
        );
      }
      break;
    default:
      break;
  }
}

export function validateReadyJobPoints(jobPoints: any[]) {
  const isInvalid = jobPoints.some((point) => {
    if (!point.status || point.status === JobPointStatusEnum.NONE) {
      throw new BadRequestException(JOB_KEYS.JOB_POINTS_MUST_HAVE_STATUS);
    }

    return (
      [JobPointStatusEnum.YELLOW, JobPointStatusEnum.RED].includes(
        point.status,
      ) &&
      (!point.notes || !point.images.length)
    );
  });

  if (isInvalid) {
    throw new BadRequestException(JOB_KEYS.JOB_POINTS_MISS_NOTES_OR_IMAGES);
  }
}

export function groupActions(actions: JobPointAction[]): JobPointAction[][] {
  const groups: { [key: string]: JobPointAction[] } = {};

  for (const action of actions) {
    if (action.isGrouped) {
      const key = `${action.isGrouped}-${action.type}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(action);
    } else {
      const key = `single-${action.type}-${Date.now()}-${Math.random()}`;
      groups[key] = [action];
    }
  }

  return Object.values(groups);
}
