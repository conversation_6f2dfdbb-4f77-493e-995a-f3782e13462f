export const JO<PERSON>_KEYS = {
  PLATFORM_NOT_SUPPORT: 'job.find.platform_not_support',
  NOT_FOUND: 'job.find.not_found',
  REPORT_CONTACT_REQUIRED: 'job.form.report_contact_required',
  ASSIGNEE_OR_EMPLOYEEES_DOES_NOT_HAVE_PERMISSION:
    'job.form.assignee_or_employee_does_not_have_permission',
  EMPLOYEE_MUST_HAVE_ASSIGNEE: 'job.form.employee_must_have_assignee',
  ONLY_ASSIGNEE_CAN_UPDATE_JOB: 'job.update.only_assignee_can_update_job',
  INSPECTION_JOB_MUST_HAVE_TYPE: 'job.create.inspection_must_have_type',
  MAINTENANCE_OR_CLEANER_JOB_ONLY_HAVE_TYPE_REGULAR_OR_PERIODIC:
    'job.create.maintenance_or_cleaner_job_only_have_type_regular_or_periodic',
  UNITS_ARE_NO_PART_OF_LOCATION: 'job.form.units_are_no_part_of_location',
  NOT_FOUND_CONTACT: 'job.form.contact_not_found',
  EQUIPMENTS_NOT_FOUND: 'job.form.equipments_not_found',
  INVALID_IMAGES: 'job.form.invalid_images',
  LOCATION_NOT_FOUND: 'job.create.location_not_found',
  PLANNED_DATE_MUST_BE_GREATER_OR_EQUAL_THAN_NOW:
    'job.form.planned_date_must_be_greater_or_equal_than_now',
  MUST_HAVE_ONE_UNIT_HAVE_POINTS: 'job.form.must_have_one_unit_have_points',
  PLANNER_DOES_NOT_HAVE_PERMISSION: 'job.form.planner_does_not_have_permission',
  MOBILE_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_OPEN_STATUS:
    'job.update.mobile_can_not_update_job_point_when_job_has_open_status',
  PORTAL_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_STATUS_IN_PROGRESS:
    'job.update.portal_cannot_update_job_point_when_job_has_status_in_progress',
  MISS_SOME_JOB_POINTS_OR_JOB_POINTS_NOT_FOUND:
    'job.update.miss_some_job_points_or_job_points_not_found',
  JOB_POINTS_MISS_NOTES_OR_IMAGES: 'job.update.job_points_miss_notes_or_images',
  JOB_POINTS_MUST_HAVE_STATUS: 'job.update.job_points_must_have_status',
  MOBILE_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_READY_STATUS:
    'job.update.mobile_can_not_update_job_point_when_job_has_ready_status',
  REVIEWER_DOES_NOT_HAVE_PERMISSION:
    'job.form.reviewer_does_not_have_permission',
  JOBS_NOT_FOUND: 'job.delete.jobs_not_found',
  ONLY_DELETE_JOB_THAT_HAVE_STATUS_OPEN_OR_CLOSED:
    'job.delete.only_delete_job_that_have_status_open_or_closed',
  API_ONLY_SUPPORT_FOR_MOBILE: 'job.find.api_only_support_for_mobile',
  JOB_STATUS_NOT_ALLOW_VIEW_SUMMARY:
    'job.view_summary.job_status_not_allow_view_summary',
  JOB_STATUS_NOT_ALLOW_VIEW_DETAIL_JOB:
    'job.find.job_status_not_allow_view_detail_job',
  JOB_STATUS_NOT_ALLOW_VIEW_REVIEW_JOB:
    'job.find.job_status_not_allow_view_review_job',
  JOB_STATUS_NOT_ALLOW_UPDATE: 'job.update.job_status_not_allow_update',
  NOT_ALLOW_UPDATE_UNITS: 'job.update.not_allow_update_unit',
  UPDATE_STATUS_SUCCESS: 'job.update_status.success',
  UPDATE_STATUS_FAILED: 'job.update_status.failed',
  INVOICE_CONTACT_NOT_FOUND: 'job.update_status.invoice_contact_not_found',
  REPORT_TO_CONTACTS_NOT_FOUND:
    'job.update_status.report_to_contacts_not_found',
  REPORT_TYPE_INTERNAL_DOES_NOT_HAVE_INVOICE_CONTACT:
    'job.update_status.report_type_internal_does_not_have_invoice_contact',
  REPORT_TYPE_INTERNAL_HAVE_ONE_DEFAULT_REPORT_TO_CONTACT:
    'job.update_status.report_type_internal_have_one_default_report_to_contact',
  EMPLOYEE_NOT_FOUND: 'job.sync.employee_not_found',
  ONLY_CO_WORKER_SYNC_DATA_FOR_JOB: 'job.sync.only_co_worker_sync_data_for_job',
  JOB_STATUS_NOT_ALLOW_SYNC_DATA: 'job.sync.job_status_not_allow_sync_data',
  CO_WORKER_CANNOT_UPDATE_JOB: 'job.update.co_worker_cannot_update_job',
  EMAIL_TEMPLATE_NOT_FOUND: 'job.send_email.email_template_not_found',
  CANNOT_CREATE_COSTLINE_FOR_JOB_HAVE_REPORT_TYPE_INTERNAL:
    'job.update.cannot_create_costline_for_job_have_report_type_internal',
  COSTLINE_ARE_NOT_IN_JOB_POINT: 'job.update.costline_are_not_in_job_point',
  INVALID_COST_TYPE: 'job.update.invalid_cost_type',
};
