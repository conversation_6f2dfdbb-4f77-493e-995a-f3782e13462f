import { faker } from '@faker-js/faker/.';
import { HttpService } from '@nestjs/axios';
import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { of, throwError } from 'rxjs';

import {
  INVENTORY_SERVICE_CLIENT,
  PDF_SERVICE_CLIENT,
} from '~/constants/app.constant';
import { AgreementlineService } from '~/modules/agreementline/agreementline.service';
import { ContactModel } from '~/modules/contact/contact.model';
import { ContactService } from '~/modules/contact/contact.service';
import { CostLineModel } from '~/modules/costline/costline.model';
import { CostTypeModel } from '~/modules/costtype/costtype.model';
import { UploadFileModel } from '~/modules/document-file/upload-file.model';
import { EmailTemplateModel } from '~/modules/email-template/email-template.model';
import { EquipmentModel } from '~/modules/equipment/equipment.model';
import { EquipmentService } from '~/modules/equipment/equipment.service';
import { IdentifierService } from '~/modules/identifier/identifier.service';
import { JobEmployeeModel } from '~/modules/job-employee/job-employee.model';
import { JobEquipmentModel } from '~/modules/job-equipment/job-equipment.model';
import { JobPointModel } from '~/modules/job-point/job-point.model';
import { JobPointService } from '~/modules/job-point/job-point.service';
import { LocationModel } from '~/modules/location/location.model';
import { NightRegistrationService } from '~/modules/night-registration/night-registration.service';
import { TenantService } from '~/modules/tenant/tenant.service';
import { TenantUserModel } from '~/modules/tenant-user/tenant-user.model';
import { TenantUserService } from '~/modules/tenant-user/tenant-user.service';
import { TokenService } from '~/modules/token/token.service';
import { UnitModel } from '~/modules/unit/unit.model';
import { UnitService } from '~/modules/unit/unit.service';
import { EmailService } from '~/processors/email/email.service';
import { CostTypeType } from '~/shared/enums/cost-type.enum';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import {
  GeneratePdfStatusEnum,
  JobFDaysEnum,
  JobFRuleEnum,
  JobPeriodTypeEnum,
  JobPointStatusEnum,
  JobReportTypeEnum,
  JobStatusEnum,
  JobStatusQueryEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';
import { JobCheckInCheckOutEnum } from '~/shared/enums/night-registration.enum';
import { TenantRoleEnum } from '~/shared/enums/tenant-role.enum';
import { WorkingDays } from '~/shared/enums/working-days.enum';
import { JOB_KEYS } from '~/shared/message-keys/job.message-keys';
import { JOB_MESSAGES } from '~/shared/messages/job.message';
import { RESERVED_MESSAGES } from '~/shared/messages/reserved.message';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import { initMockCostline, mockCostlineData } from '~/test/mocks/costline.mock';
import { initMockCostType, mockCostTypeData } from '~/test/mocks/costtype.mock';
import {
  initMockEquipment,
  mockEquipmentData,
} from '~/test/mocks/equipment.mock';
import { initMockJob, mockJobData } from '~/test/mocks/job.mock';
import { initMockJobEmployee } from '~/test/mocks/jobemployee.mock';
import { initMockJobPoint, mockJobPointData } from '~/test/mocks/jobpoint.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { mockNightRegistrationReservationData } from '~/test/mocks/nightregistrationreservation.mock';
import { mockStorageData } from '~/test/mocks/storage.mock';
import { mockTeamData } from '~/test/mocks/team.mock';
import { mockTenantData } from '~/test/mocks/tenant.mock';
import {
  initMockTenantUser,
  mockTenantUserData,
} from '~/test/mocks/tenantuser.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';
import { initMockUploadFile } from '~/test/mocks/uploadfile.mock';

import {
  CreateJobDto,
  CreateMultipleDaysJobDto,
  JobQuerySchemaDto,
  JobUpdateStatusDto,
  MobileJobQuerySchemaDto,
  UpdateOpenMultipleDaysJobDto,
  UpdateScheduleJobSettingsDto,
} from '../dtos/job.dto';
import * as jobHelper from '../job.helper';
import { JobModel } from '../job.model';
import { JobService } from '../job.service';
import { jobTest } from './job.dto.test';
describe('JobService', () => {
  let service: JobService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        JobService,
        JobPointService,
        ...testInjectProviders([
          ContactModel,
          LocationModel,
          TenantUserModel,
          JobModel,
          JobPointModel,
          JobEmployeeModel,
          EquipmentModel,
          UnitModel,
          UploadFileModel,
          EmailTemplateModel,
          CostLineModel,
          CostTypeModel,
          JobEquipmentModel,
          // Services
          UnitService,
          IdentifierService,
          TenantUserService,
          ContactService,
          EquipmentService,
          TenantService,
          EmailService,
          NightRegistrationService,
          HttpService,
          AgreementlineService,
          TokenService,
        ]),
        {
          provide: INVENTORY_SERVICE_CLIENT,
          useValue: {
            send: jest.fn().mockReturnValue(of({ data: {} })),
          },
        },
        {
          provide: PDF_SERVICE_CLIENT,
          useValue: {
            send: jest.fn().mockReturnValue(of({ data: {} })),
          },
        },
      ],
    }).compile();

    service = module.get(JobService);

    await Promise.all([
      initMockJob({ type: JobPeriodTypeEnum.REGULAR }),
      initMockJobEmployee(),
      initMockEquipment(),
      initMockUnit(),
      initMockLocation(),
      initMockContact(),
      initMockJobEmployee(),
      initMockTenantUser(),
      initMockJobPoint(),
      initMockEquipment(),
    ]);
  });

  afterAll(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should call fn and return list jobs', async () => {
      jest
        .spyOn(service as any, 'getAllowedJobStatusForGetAllJob')
        .mockReturnValue(Object.values(JobStatusEnum));

      const payload: JobQuerySchemaDto = {
        user: mockTenantUserData._id.toString(),
        jobType: JobTypeEnum.INSPECTION,
        status: JobStatusQueryEnum.COMPLETED,
        units: [mockUnitData._id.toString()],
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        team: mockTeamData._id.toString(),
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(jobTest.findAllSchema);
    });

    it('should call fn and return list empty jobs if no jobs found', async () => {
      const payload: JobQuerySchemaDto = {
        user: mockTenantUserData._id.toString(),
        jobType: JobTypeEnum.INSPECTION,
        status: JobStatusQueryEnum.COMPLETED,
        units: mockUnitData._id.toString(),
        endDate: new Date().toISOString(),
        schedulerId: '00000',
        location: mockLocationData._id.toString(),
        assignee: mockTenantUserData._id.toString(),
        planner: mockTenantUserData._id.toString(),
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });

    it('should call fn and return list multiple days jobs', async () => {
      jest
        .spyOn(service as any, 'getAllowedJobStatusForGetAllJob')
        .mockReturnValue(Object.values(JobStatusQueryEnum));
      const multipleDayJobId = new ObjectId();
      await initMockJob({
        _id: multipleDayJobId,
        type: JobPeriodTypeEnum.MULTIPLE_DAYS,
        status: JobStatusEnum.OPEN,
        plannedDate: dayjs().add(1, 'day').toDate(),
        plannedEndDate: dayjs().add(6, 'day').toDate(),
      });
      const payload: JobQuerySchemaDto = {
        user: mockTenantUserData._id.toString(),
        jobType: JobTypeEnum.INSPECTION,
        status: JobStatusQueryEnum.OPEN,
        type: JobPeriodTypeEnum.MULTIPLE_DAYS,
        startDate: dayjs().toISOString(),
        endDate: dayjs().add(8, 'day').toISOString(),
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(jobTest.findAllSchema);
      const foundMultipleDayJob = result.docs.find(
        (job) => job._id.toString() === multipleDayJobId.toString(),
      );
      expect(foundMultipleDayJob).toBeDefined();
    });
  });

  describe('findAllMobile', () => {
    it('should throw error if platform is not provided', async () => {
      await expect(service.findAllMobile({} as any)).rejects.toThrow(
        JOB_KEYS.API_ONLY_SUPPORT_FOR_MOBILE,
      );
    });

    it('should call fn and return list jobs', async () => {
      jest.spyOn(service as any, 'validateRoleOfUser').mockReturnValue(true);

      const payload: MobileJobQuerySchemaDto = {
        platform: 'mb',
        user: mockTenantUserData._id.toString(),
        lastSyncedAt: dayjs().subtract(1, 'day').toISOString(),
      };

      const result = await service.findAllMobile(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(jobTest.findAllMobileSchema);
    });

    it('should call fn and return list empty jobs if no jobs found', async () => {
      const payload: MobileJobQuerySchemaDto = {
        platform: 'mb',
        user: mockTenantUserData._id.toString(),
        isDeleted: 'true',
      };

      const result = await service.findAllMobile(payload);
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });

  describe('findReviewDetail', () => {
    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();
      await expect(service.findReviewDetail({ id: jobId })).rejects.toThrow(
        JOB_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if job status not allow view review in platform mobile', async () => {
      await initMockJob({ status: JobStatusEnum.READY });

      await expect(
        service.findReviewDetail({
          id: mockJobData._id.toString(),
          platform: 'mb',
        }),
      ).rejects.toThrow(JOB_KEYS.JOB_STATUS_NOT_ALLOW_VIEW_REVIEW_JOB);
    });

    it('should throw error if current user is not in employees list when using mobile platform', async () => {
      await initMockJob({ status: JobStatusEnum.IN_PROGRESS });

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        user: new ObjectId().toString(),
      };

      await expect(service.findReviewDetail(payload)).rejects.toThrow(
        JOB_KEYS.NOT_FOUND,
      );
    });

    it('should return review detail successfully for portal platform', async () => {
      const parentUnitId = new ObjectId();
      await Promise.all([
        initMockUnit({
          _id: parentUnitId,
          isRoot: true,
          parent: null,
        }),
        initMockUnit({
          parent: parentUnitId,
          isRoot: false,
        }),
        initMockJob({ status: JobStatusEnum.COMPLETE }),
        initMockJobEmployee(),
        initMockJobPoint(),
      ]);

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'portal',
      };

      const result = await service.findReviewDetail(payload);

      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findReviewDetailSchema);
    });

    it('should return review detail successfully for mobile platform with valid user', async () => {
      await initMockJob({ status: JobStatusEnum.IN_PROGRESS });

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        user: mockTenantUserData._id.toString(),
      };

      const result = await service.findReviewDetail(payload);

      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findReviewDetailSchema);
    });

    it('should return grouped job points by units with sorted cost lines', async () => {
      await Promise.all([
        initMockJob({ status: JobStatusEnum.COMPLETE }),
        initMockJobPoint({
          _id: new ObjectId(),
          position: 1,
          description: 'Test job point 1',
        }),
        initMockJobPoint({
          _id: new ObjectId(),
          position: 2,
          description: 'Test job point 2',
        }),
      ]);

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'portal',
      };

      const result = await service.findReviewDetail(payload);

      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findReviewDetailSchema);

      // Verify specific business logic
      expect(result.units).toBeDefined();
      expect(Array.isArray(result.units)).toBe(true);

      if (result.units.length > 0) {
        expect(result.units[0]).toHaveProperty('points');
        expect(Array.isArray(result.units[0].points)).toBe(true);

        // Verify job points are sorted by position
        if (result.units[0].points.length > 1) {
          const points = result.units[0].points;
          for (let i = 1; i < points.length; i++) {
            expect(points[i].position).toBeGreaterThanOrEqual(
              points[i - 1].position,
            );
          }
        }
      }
    });
  });

  describe('findOne', () => {
    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();

      await expect(service.findOne({ id: jobId })).rejects.toThrow(
        JOB_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if job status not allow view detail job when using mobile platform', async () => {
      await initMockJob({ status: JobStatusEnum.READY });

      await expect(
        service.findOne({ id: mockJobData._id.toString(), platform: 'mb' }),
      ).rejects.toThrow(JOB_KEYS.JOB_STATUS_NOT_ALLOW_VIEW_DETAIL_JOB);
    });

    it('should throw error if current user is not in employees list when using mobile platform', async () => {
      await initMockJob();

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        user: new ObjectId().toString(),
      };

      await expect(service.findOne(payload)).rejects.toThrow(
        JOB_KEYS.NOT_FOUND,
      );
    });

    it('should call fn and return job detail when using mobile platform', async () => {
      const payload = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        user: mockTenantUserData._id.toString(),
      };

      const result = await service.findOne(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockJobData._id);
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should call fn and return job detail with job status open', async () => {
      await initMockJob({ status: JobStatusEnum.OPEN });

      const payload = {
        id: mockJobData._id.toString(),
        user: mockTenantUserData._id.toString(),
      };

      const result = await service.findOne(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockJobData._id);
      expect(result).toHaveProperty('jobPoints');
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });
  });

  describe('create', () => {
    beforeEach(() => {
      jest
        .spyOn(service as any, 'validatePermissionOfUser')
        .mockReturnValue(true);
      jest
        .spyOn(service['identifierService'], 'generateIdentifier')
        .mockResolvedValue(nanoid(12));
    });

    const createPayload: CreateJobDto = {
      platform: 'portal',
      user: mockTenantUserData._id.toString(),
      type: JobPeriodTypeEnum.REGULAR,
      jobType: JobTypeEnum.INSPECTION,
      plannedDate: new Date().toISOString(),
      assignee: mockTenantUserData._id.toString(),
      location: mockLocationData._id.toString(),
      units: [
        {
          _id: mockUnitData._id.toString(),
          points: [
            {
              position: mockJobPointData.position,
              description: mockJobPointData.description,
            },
          ],
        },
      ],
      employees: [
        {
          employee: mockTenantUserData._id.toString(),
          estimatedHours: 2,
        },
      ],
      instructions: '',
      equipments: [
        {
          equipment: mockEquipmentData._id.toString(),
        },
      ],
    };

    it('should throw error if user not permission to create job', async () => {
      jest
        .spyOn(service as any, 'validatePermissionOfUser')
        .mockReturnValue(false);
      await expect(service.create(createPayload)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw error if units is not provided', async () => {
      const invalidPayload = {
        ...createPayload,
        jobType: JobTypeEnum.CLEANING,
        units: [],
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        JOB_KEYS.MUST_HAVE_ONE_UNIT_HAVE_POINTS,
      );
    });

    it('should throw error if position of units is not unique', async () => {
      const invalidPayload = {
        ...createPayload,
        jobType: JobTypeEnum.MAINTENANCE,
        units: [
          {
            _id: mockUnitData._id.toString(),
            points: [
              {
                _id: mockJobPointData._id.toString(),
                position: 1,
                description: mockJobPointData.description,
              },
              {
                _id: new ObjectId().toString(),
                position: 1,
                description: 'Duplicate Point',
              },
            ],
          },
        ],
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw error if position of units is not consecutive', async () => {
      const invalidPayload = {
        ...createPayload,
        units: [
          {
            _id: mockUnitData._id.toString(),
            points: [
              {
                _id: mockJobPointData._id.toString(),
                position: 2,
                description: mockJobPointData.description,
              },
              {
                _id: new ObjectId().toString(),
                position: 5,
                description: 'Non-consecutive Point',
              },
            ],
          },
        ],
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw error if has report type but contact is not provided', async () => {
      const invalidPayload = {
        ...createPayload,
        reportType: JobReportTypeEnum.CUSTOMER,
        contact: undefined,
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        JOB_KEYS.REPORT_CONTACT_REQUIRED,
      );
    });

    it('should throw error if location not found', async () => {
      const invalidPayload = {
        ...createPayload,
        location: new ObjectId().toString(),
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        JOB_KEYS.LOCATION_NOT_FOUND,
      );
    });

    it('should throw error if units is not are part of location', async () => {
      const invalidPayload = {
        ...createPayload,
        units: [
          {
            _id: new ObjectId().toString(),
            points: [
              {
                _id: mockJobPointData._id.toString(),
                position: mockJobPointData.position,
                description: mockJobPointData.description,
              },
            ],
          },
        ],
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        JOB_KEYS.UNITS_ARE_NO_PART_OF_LOCATION,
      );
    });

    it('should throw error if assignee different from employees', async () => {
      const invalidPayload = {
        ...createPayload,
        assignee: new ObjectId().toString(),
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        JOB_KEYS.EMPLOYEE_MUST_HAVE_ASSIGNEE,
      );
    });

    it('should throw error if assignee or employees does not have permission', async () => {
      await expect(service.create(createPayload)).rejects.toThrow(
        JOB_KEYS.ASSIGNEE_OR_EMPLOYEEES_DOES_NOT_HAVE_PERMISSION,
      );
    });

    it('should throw error if has report type but contact not found', async () => {
      jest
        .spyOn(service as any, 'validateRoleOfEmployees')
        .mockResolvedValue(undefined);

      const invalidPayload = {
        ...createPayload,
        reportType: JobReportTypeEnum.CUSTOMER,
        reportContact: new ObjectId().toString(),
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        JOB_KEYS.NOT_FOUND_CONTACT,
      );
    });

    it('should throw error if equipments not found', async () => {
      const invalidPayload = {
        ...createPayload,
        equipments: [
          {
            equipment: new ObjectId().toString(),
          },
        ],
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        JOB_KEYS.EQUIPMENTS_NOT_FOUND,
      );
    });

    it('should throw error if images are invalid', async () => {
      const invalidPayload = {
        ...createPayload,
        images: ['invalid-image-url.exe'],
      };
      await expect(service.create(invalidPayload)).rejects.toThrow(
        JOB_KEYS.INVALID_IMAGES,
      );
    });

    it('should call fn and create job successfully', async () => {
      const result = await service.create(createPayload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should call fn and create job successfully when platform mobile', async () => {
      jest
        .spyOn(service['inventoryClient'], 'send')
        .mockReturnValue(of({ data: {} }) as any);

      const mobilePayload: CreateJobDto = {
        ...createPayload,
        type: JobPeriodTypeEnum.PERIODIC,
        platform: 'mb',
        reserved: {
          storage: new ObjectId().toString(),
          reservedDetails: [{ article: '', amount: 0 }],
        },
      };
      const result = await service.create(mobilePayload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should call fn and create job without employees', async () => {
      const payloadWithoutEmployees: any = {
        ...omit(createPayload, 'employees'),
      };
      const result = await service.create(payloadWithoutEmployees);
      expect(result).toBeDefined();
      expect(result.employees).toHaveLength(0);
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should call create multiple days job successfully', async () => {
      const plannedDate = dayjs().toISOString();
      const plannedEndDate = dayjs(plannedDate).add(1, 'day').toISOString();
      const multiDayPayload: CreateMultipleDaysJobDto = {
        ...createPayload,
        type: JobPeriodTypeEnum.MULTIPLE_DAYS,
        plannedDate: plannedDate,
        plannedEndDate: plannedEndDate,
        employees: [
          {
            employee: mockTenantUserData._id.toString(),
            estimatedHours: 60,
            plannedDate: plannedDate,
          },
          {
            employee: mockTenantUserData._id.toString(),
            estimatedHours: 0,
            plannedDate: plannedEndDate,
          },
        ],
        equipments: [
          {
            equipment: mockEquipmentData._id.toString(),
            plannedDate: plannedDate,
          },
        ],
      };
      const result = await service.create(multiDayPayload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });
  });

  describe('updateJob', () => {
    const updatePayload = {
      user: mockTenantUserData._id.toString(),
      title: 'Updated Job Title',
      status: JobStatusEnum.OPEN,
      jobType: JobTypeEnum.INSPECTION,
      instructions: 'Updated instructions',
      images: ['https://example.com/image.jpg'],
      assignee: mockTenantUserData._id.toString(),
      equipments: [
        {
          equipment: mockEquipmentData._id.toString(),
        },
      ],
      employees: [
        { employee: mockTenantUserData._id.toString(), estimatedHours: 2 },
      ],
      units: [
        {
          _id: mockUnitData._id.toString(),
          points: [
            {
              position: mockJobPointData.position,
              description: mockJobPointData.description,
            },
          ],
        },
      ],
    };

    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();
      await expect(service.updateJob(jobId, {}, {})).rejects.toThrow(
        JOB_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if current user is not in employees list when using mobile platform', async () => {
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        platform: 'mb',
        user: new ObjectId().toString(),
      };

      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND);
    });

    it('should throw error if has report type but report contact is not provided', async () => {
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        reportType: JobReportTypeEnum.CUSTOMER,
        reportContact: undefined,
      };
      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.REPORT_CONTACT_REQUIRED);
    });

    it('should throw error if images is not valid', async () => {
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        images: ['invalid-image-url.exe'],
      };
      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.INVALID_IMAGES);
    });

    it('should throw error if assignee different from employees', async () => {
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        assignee: new ObjectId().toString(),
      };
      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.EMPLOYEE_MUST_HAVE_ASSIGNEE);
    });

    it('should throw error if assignee or employees does not have permission', async () => {
      jest.restoreAllMocks();
      const jobId = mockJobData._id.toString();

      await expect(service.updateJob(jobId, updatePayload, {})).rejects.toThrow(
        JOB_KEYS.ASSIGNEE_OR_EMPLOYEEES_DOES_NOT_HAVE_PERMISSION,
      );
    });

    it('should throw error if co-worker update job when using mobile platform', async () => {
      jest
        .spyOn(service as any, 'validateEmployees')
        .mockResolvedValue(undefined);

      await initMockJob({ assignee: new ObjectId() });

      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        platform: 'mb',
      };
      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.CO_WORKER_CANNOT_UPDATE_JOB);
    });

    it('should throw error if has report type but contact not found', async () => {
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        reportType: JobReportTypeEnum.CUSTOMER,
        reportContact: new ObjectId().toString(),
      };
      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND_CONTACT);
    });

    it('should throw error if equipments not found', async () => {
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        equipments: [
          {
            equipment: new ObjectId().toString(),
          },
        ],
      };
      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.EQUIPMENTS_NOT_FOUND);
    });

    it('should throw error if planner does not have permission', async () => {
      jest
        .spyOn(service['tenantUserService'], 'verifyRoles')
        .mockResolvedValue(false);

      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        planner: new ObjectId().toString(),
      };

      jest
        .spyOn(jobHelper, 'validateUpdateData')
        .mockReturnValue(invalidPayload);

      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.PLANNER_DOES_NOT_HAVE_PERMISSION);
    });

    it('should throw error if units is not are part of location', async () => {
      jest.restoreAllMocks();
      jest
        .spyOn(service as any, 'validateEmployees')
        .mockResolvedValue(undefined);

      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        units: [
          {
            _id: new ObjectId().toString(),
            points: [
              {
                position: mockJobPointData.position,
                description: mockJobPointData.description,
              },
            ],
          },
        ],
      };
      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.UNITS_ARE_NO_PART_OF_LOCATION);
    });

    it('should throw error if units is not provided', async () => {
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        units: [],
      };

      jest
        .spyOn(jobHelper, 'validateUpdateData')
        .mockReturnValue(invalidPayload);

      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.MUST_HAVE_ONE_UNIT_HAVE_POINTS);
    });

    it('should throw error if update units when job status is not OPEN', async () => {
      jest.restoreAllMocks();
      jest
        .spyOn(service as any, 'validateEmployees')
        .mockResolvedValue(undefined);

      const unitId = new ObjectId();
      await initMockUnit({ _id: unitId });
      await initMockJob({ status: JobStatusEnum.IN_PROGRESS });

      const jobId = mockJobData._id.toString();

      const invalidPayload = {
        ...updatePayload,
        units: [
          {
            _id: unitId.toString(),
            points: [
              {
                position: mockJobPointData.position,
                description: mockJobPointData.description,
              },
            ],
          },
        ],
      };

      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(JOB_KEYS.NOT_ALLOW_UPDATE_UNITS);
    });

    it('should throw error if update job status open when using mobile platform', async () => {
      await initMockJob({
        assignee: mockTenantUserData._id,
        status: JobStatusEnum.OPEN,
      });
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        platform: 'mb',
        status: JobStatusEnum.OPEN,
      };

      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(
        JOB_KEYS.MOBILE_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_OPEN_STATUS,
      );
    });

    it('should throw error if update job status in progress when using difference platform mobile', async () => {
      await initMockJob({
        assignee: mockTenantUserData._id,
        status: JobStatusEnum.IN_PROGRESS,
      });
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        platform: 'portal',
        status: JobStatusEnum.IN_PROGRESS,
      };

      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(
        JOB_KEYS.PORTAL_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_STATUS_IN_PROGRESS,
      );
    });

    it('should throw error if update job status ready when using mobile platform ', async () => {
      await initMockJob({
        assignee: mockTenantUserData._id,
        status: JobStatusEnum.READY,
      });
      const jobId = mockJobData._id.toString();
      const invalidPayload = {
        ...updatePayload,
        platform: 'mb',
        status: JobStatusEnum.READY,
      };

      await expect(
        service.updateJob(jobId, invalidPayload, {}),
      ).rejects.toThrow(
        JOB_KEYS.MOBILE_CANNOT_UPDATE_JOB_POINT_WHEN_JOB_HAS_READY_STATUS,
      );
    });

    it('should call fn and update job status ready', async () => {
      jest
        .spyOn(service as any, 'bulkWriteJobPoints')
        .mockResolvedValue(undefined);

      await initMockJob({
        type: JobPeriodTypeEnum.REGULAR,
        status: JobStatusEnum.READY,
      });

      const payload = {
        ...omit(updatePayload, [
          'title',
          'jobType',
          'instructions',
          'images',
          'assignee',
          'equipments',
        ]),
        status: JobStatusEnum.READY,
        invoiceContact: mockContactData._id.toString(),
        isSendRC: true,
        isSendRR: true,
        rtContacts: [mockContactData._id.toString()],
        rrContacts: [mockContactData._id.toString()],
        employees: [
          {
            employee: mockTenantUserData._id.toString(),
            actualHours: 2,
          },
        ],
        units: [
          {
            _id: mockUnitData._id.toString(),
            points: [
              {
                _id: mockJobPointData._id.toString(),
                images: ['https://example.com/image.jpg'],
                notes: 'Updated Job Point Notes',
                actions: [],
              },
            ],
            reservations: [
              {
                reservation:
                  mockNightRegistrationReservationData._id.toString(),
                type: JobCheckInCheckOutEnum.CHECKIN,
                checked: true,
              },
            ],
          },
        ],
      };

      const jobId = mockJobData._id.toString();
      const result = await service.updateJob(jobId, payload, {});
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockJobData._id);
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should call fn and update job status in progress', async () => {
      jest
        .spyOn(service['tenantUserService'], 'verifyRoles')
        .mockResolvedValue(true);

      await initMockJob({
        type: JobPeriodTypeEnum.REGULAR,
        jobType: JobTypeEnum.CLEANING,
        status: JobStatusEnum.IN_PROGRESS,
      });

      const payload = {
        ...omit(updatePayload, ['title', 'jobType', 'units']),
        status: JobStatusEnum.IN_PROGRESS,
        planner: mockTenantUserData._id.toString(),
        plannedDate: new Date().toISOString(),
        reserved: {
          storage: new ObjectId().toString(),
          reservedDetails: [{ article: '', amount: 10 }],
        },
      };

      const jobId = mockJobData._id.toString();

      // Case 1: With reversed id
      await service.updateJob(
        jobId,
        {
          ...payload,
          reserved: {
            _id: new ObjectId().toString(),
            storage: new ObjectId().toString(),
            reservedDetails: [{ article: '', amount: 10 }],
          },
        },
        {},
      );

      // Case 2: Without reversed id
      const result = await service.updateJob(jobId, payload, {});
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockJobData._id);
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should call fn and update job type periodic', async () => {
      await initMockJob({
        type: JobPeriodTypeEnum.PERIODIC,
        status: JobStatusEnum.OPEN,
      });

      const payload = {
        ...updatePayload,
        portal: 'mb',
        actualHours: 2,
        type: JobPeriodTypeEnum.PERIODIC,
        status: JobStatusEnum.OPEN,
      };

      jest.spyOn(jobHelper, 'validateUpdateData').mockReturnValue(payload);

      const jobId = mockJobData._id.toString();
      const result = await service.updateJob(jobId, payload, {});
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockJobData._id);
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should call fn and update job type multiple days', async () => {
      const newMultipleDaysJobId = new ObjectId();
      await initMockJob({
        _id: newMultipleDaysJobId,
        type: JobPeriodTypeEnum.MULTIPLE_DAYS,
        status: JobStatusEnum.OPEN,
      });

      const plannedDate = dayjs().toISOString();
      const plannedEndDate = dayjs(plannedDate).add(1, 'day').toISOString();
      const payload: UpdateOpenMultipleDaysJobDto = {
        ...updatePayload,
        plannedDate: plannedDate,
        plannedEndDate: plannedEndDate,
        employees: [
          {
            employee: mockTenantUserData._id.toString(),
            estimatedHours: 60,
            plannedDate: plannedDate,
          },
          {
            employee: mockTenantUserData._id.toString(),
            estimatedHours: 0,
            plannedDate: plannedEndDate,
          },
        ],
        equipments: [
          {
            equipment: mockEquipmentData._id.toString(),
            plannedDate: plannedDate,
          },
        ],
        isActive: true,
        planner: mockTenantUserData._id.toString(),
      };

      const result = await service.updateJob(
        newMultipleDaysJobId.toString(),
        payload,
        {},
      );
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', newMultipleDaysJobId);
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });
  });

  describe('deleteJobs', () => {
    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();
      await expect(
        service.deleteJobs({ jobIds: [jobId] } as any),
      ).rejects.toThrow(JOB_KEYS.JOBS_NOT_FOUND);
    });

    it('should throw error if job status is not OPEN', async () => {
      await initMockJob({ status: JobStatusEnum.IN_PROGRESS });

      const jobId = mockJobData._id.toString();
      await expect(
        service.deleteJobs({ jobIds: [jobId] } as any),
      ).rejects.toThrow(
        JOB_KEYS.ONLY_DELETE_JOB_THAT_HAVE_STATUS_OPEN_OR_CLOSED,
      );
    });

    it('should throw error if job status is not CLOSED', async () => {
      const jobId = mockJobData._id.toString();
      await expect(
        service.deleteJobs({ jobIds: [jobId] } as any),
      ).rejects.toThrow(
        JOB_KEYS.ONLY_DELETE_JOB_THAT_HAVE_STATUS_OPEN_OR_CLOSED,
      );
    });

    it('should throw error if user does not have permission to delete job', async () => {
      jest
        .spyOn(service['tenantUserService'], 'verifyRoles')
        .mockResolvedValue(false);

      await initMockJob({ status: JobStatusEnum.OPEN });

      const jobId = mockJobData._id.toString();
      const payload: any = {
        jobIds: [jobId],
        user: mockTenantUserData._id.toString(),
      };

      await expect(service.deleteJobs(payload)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should call fn and delete job successfully', async () => {
      jest
        .spyOn(service as any, 'validatePermissionOfUser')
        .mockReturnValue(true);

      await initMockJob({ status: JobStatusEnum.OPEN });

      const jobId = mockJobData._id.toString();
      const payload: any = {
        jobIds: [jobId],
        user: mockTenantUserData._id.toString(),
      };

      const result = await service.deleteJobs(payload);
      expect(result).toBeDefined();
      expect(result.acknowledged).toBe(true);
      expect(result.modifiedCount).toBeGreaterThan(0);
    });
  });

  describe('updateStatus', () => {
    const updatePayload: JobUpdateStatusDto = {
      id: mockJobData._id.toString(),
      platform: 'portal',
      status: JobStatusEnum.COMPLETE,
      user: mockTenantUserData._id.toString(),
      headers: {},
    };

    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();
      await expect(service.updateStatus({ id: jobId })).rejects.toThrow(
        JOB_KEYS.NOT_FOUND,
      );
    });

    it('should throw error when validate ready job point missing status', async () => {
      await initMockJob({ status: JobStatusEnum.IN_PROGRESS });

      const payload = {
        ...updatePayload,
        status: JobStatusEnum.READY,
        platform: 'mb',
        units: [{ points: [{ status: JobPointStatusEnum.NONE }] }],
      };

      await expect(service.updateStatus(payload)).rejects.toThrow(
        JOB_KEYS.JOB_POINTS_MUST_HAVE_STATUS,
      );
    });

    it('should throw error when validate update status ready job point invalid data', async () => {
      const payload = {
        ...updatePayload,
        status: JobStatusEnum.READY,
        platform: 'mb',
        units: [
          {
            points: [
              {
                status: JobPointStatusEnum.YELLOW,
                position: 1,
                notes: '',
                images: [],
              },
            ],
          },
        ],
      };

      await expect(service.updateStatus(payload)).rejects.toThrow(
        JOB_KEYS.JOB_POINTS_MISS_NOTES_OR_IMAGES,
      );
    });

    it('should throw error when validate update status reject job does not permission', async () => {
      jest
        .spyOn(service['tenantUserService'], 'verifyRoles')
        .mockResolvedValue(false);

      await initMockJob({ status: JobStatusEnum.READY });

      const payload = {
        ...updatePayload,
        status: JobStatusEnum.REJECT,
        platform: 'portal',
      };

      await expect(service.updateStatus(payload)).rejects.toThrow(
        JOB_KEYS.REVIEWER_DOES_NOT_HAVE_PERMISSION,
      );
    });

    it('should call fn and update job status successfully', async () => {
      await initMockContact({ isInternal: true });

      const payload = {
        ...updatePayload,
        status: JobStatusEnum.COMPLETE,
        platform: 'portal',

        rtContacts: [mockContactData._id.toString()],
      };

      const result = await service.updateStatus(payload);
      expect(result).toBeDefined();
      expect(result.message).toBe(JOB_KEYS.UPDATE_STATUS_SUCCESS);
    });
  });

  describe('validateUpdateJobFlow', () => {
    const invalidMobileCases = [
      // [currentStatus, nextStatus]
      [JobStatusEnum.OPEN, JobStatusEnum.READY],
      [JobStatusEnum.OPEN, JobStatusEnum.COMPLETE],
      [JobStatusEnum.OPEN, JobStatusEnum.REJECT],
      [JobStatusEnum.IN_PROGRESS, JobStatusEnum.OPEN],
      [JobStatusEnum.IN_PROGRESS, JobStatusEnum.COMPLETE],
      [JobStatusEnum.IN_PROGRESS, JobStatusEnum.REJECT],
      [JobStatusEnum.READY, JobStatusEnum.OPEN],
      [JobStatusEnum.READY, JobStatusEnum.IN_PROGRESS],
      [JobStatusEnum.READY, JobStatusEnum.COMPLETE],
      [JobStatusEnum.READY, JobStatusEnum.REJECT],
      [JobStatusEnum.COMPLETE, JobStatusEnum.OPEN],
      [JobStatusEnum.COMPLETE, JobStatusEnum.IN_PROGRESS],
      [JobStatusEnum.COMPLETE, JobStatusEnum.READY],
      [JobStatusEnum.COMPLETE, JobStatusEnum.REJECT],
      [JobStatusEnum.REJECT, JobStatusEnum.OPEN],
      [JobStatusEnum.REJECT, JobStatusEnum.IN_PROGRESS],
      [JobStatusEnum.REJECT, JobStatusEnum.READY],
      [JobStatusEnum.REJECT, JobStatusEnum.COMPLETE],
    ];

    test.each(invalidMobileCases)(
      'should throw error if update status from %s to %s on mobile',
      async (currentStatus: any, nextStatus) => {
        await initMockJob({ status: currentStatus });
        const jobId = mockJobData._id.toString();
        const payload = {
          id: jobId,
          status: nextStatus,
          user: mockTenantUserData._id.toString(),
          platform: 'mb',
        };
        await expect(service.updateStatus(payload)).rejects.toThrow(
          new BadRequestException('Invalid job status flow'),
        );
      },
    );

    const invalidPortalCases = [
      [JobStatusEnum.OPEN, JobStatusEnum.READY],
      [JobStatusEnum.OPEN, JobStatusEnum.COMPLETE],
      [JobStatusEnum.OPEN, JobStatusEnum.REJECT],
      [JobStatusEnum.IN_PROGRESS, JobStatusEnum.READY],
      [JobStatusEnum.IN_PROGRESS, JobStatusEnum.COMPLETE],
      [JobStatusEnum.IN_PROGRESS, JobStatusEnum.REJECT],
      [JobStatusEnum.READY, JobStatusEnum.OPEN],
      [JobStatusEnum.READY, JobStatusEnum.IN_PROGRESS],
      [JobStatusEnum.COMPLETE, JobStatusEnum.READY],
      [JobStatusEnum.COMPLETE, JobStatusEnum.REJECT],
      [JobStatusEnum.REJECT, JobStatusEnum.READY],
      [JobStatusEnum.REJECT, JobStatusEnum.COMPLETE],
    ];

    test.each(invalidPortalCases)(
      'should throw error if update status from %s to %s on portal',
      async (currentStatus: any, nextStatus) => {
        await initMockJob({ status: currentStatus });
        const jobId = mockJobData._id.toString();
        const payload = {
          id: jobId,
          status: nextStatus,
          user: mockTenantUserData._id.toString(),
          platform: 'portal',
        };
        await expect(service.updateStatus(payload)).rejects.toThrow(
          new BadRequestException('Invalid job status flow'),
        );
      },
    );
  });

  describe('createScheduleJob', () => {
    const createScheduleJobPayload = {
      fIdentifier: 'SCHEDULE_001',
      fInterval: 1,
      fRule: JobFRuleEnum.DAILY,
      fStartDate: dayjs().add(1, 'day').toISOString(),
      fEndDate: dayjs().add(1, 'month').toISOString(),
      platform: 'portal',
      user: mockTenantUserData._id.toString(),
      type: JobPeriodTypeEnum.PERIODIC as const,
      jobType: JobTypeEnum.INSPECTION,
      instructions: 'Test schedule job instructions',
      assignee: mockTenantUserData._id.toString(),
      location: mockLocationData._id.toString(),
      units: [
        {
          _id: mockUnitData._id.toString(),
          points: [
            {
              position: mockJobPointData.position,
              description: mockJobPointData.description,
            },
          ],
        },
      ],
      employees: [
        {
          employee: mockTenantUserData._id.toString(),
          estimatedHours: 2,
        },
      ],
      equipments: [
        {
          equipment: mockEquipmentData._id.toString(),
        },
      ],
    };

    const expectSuccessfulScheduleJobCreation = async (
      payload: any,
      expectedIdentifier: string,
    ) => {
      const result = await service.createScheduleJob(
        mockJobData._id.toString(),
        payload,
      );

      expect(result).toBeUndefined();
      const updatedJob = await service['jobModel']
        .findById(mockJobData._id)
        .lean();
      expect(updatedJob).toBeDefined();
      expect(updatedJob!.fIdentifier).toBe(expectedIdentifier);
    };

    beforeAll(async () => {
      await initMockJobPoint();
      await initMockJobEmployee();
      await initMockJob({
        type: JobPeriodTypeEnum.PERIODIC,
        status: JobStatusEnum.OPEN,
      });

      jest
        .spyOn(service['identifierService'], 'generateIdentifier')
        .mockResolvedValue(nanoid(12));
    });

    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();
      await expect(
        service.createScheduleJob(jobId, createScheduleJobPayload),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND);
    });

    it('should create schedule job successfully and update fIdentifier', async () => {
      await expectSuccessfulScheduleJobCreation(
        createScheduleJobPayload,
        createScheduleJobPayload.fIdentifier,
      );
    });

    const scheduleRuleTestCases = [
      {
        description: 'weekly rule and fDays',
        payload: {
          fRule: JobFRuleEnum.WEEKLY,
          fDays: [JobFDaysEnum.MONDAY, JobFDaysEnum.FRIDAY],
        },
      },
      {
        description: 'monthly rule and fDayInMonth',
        payload: {
          fRule: JobFRuleEnum.MONTHLY,
          fDayInMonth: 15,
        },
      },
      {
        description: 'monthly rule having both fDays and fDayInMonth',
        payload: {
          fRule: JobFRuleEnum.MONTHLY,
          fDays: [JobFDaysEnum.MONDAY],
          fDayInMonth: 2,
        },
      },
      {
        description:
          'monthly rule having fDays and fDayInMonth = 5 (last occurrence)',
        payload: {
          fRule: JobFRuleEnum.MONTHLY,
          fDays: [JobFDaysEnum.FRIDAY],
          fDayInMonth: 5,
        },
      },
      {
        description: 'yearly rule and fDayInMonth',
        payload: {
          fRule: JobFRuleEnum.YEARLY,
          fDayInMonth: 1,
        },
      },
    ];

    test.each(scheduleRuleTestCases)(
      'should create schedule job with $description',
      async ({ payload }) => {
        const fullPayload = { ...createScheduleJobPayload, ...payload };
        await expectSuccessfulScheduleJobCreation(
          fullPayload,
          fullPayload.fIdentifier,
        );
      },
    );
  });

  describe('updateScheduleJob', () => {
    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();
      await expect(
        service.updateScheduleJob(jobId, true, 'portal'),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND);
    });

    it('should updateScheduleJob successfully without overriding regular jobs', async () => {
      await initMockJob({
        type: JobPeriodTypeEnum.PERIODIC,
        fIdentifier: 'SCHEDULE_TEST',
        status: JobStatusEnum.OPEN,
      });

      const result = await service.updateScheduleJob(
        mockJobData._id.toString(),
        false,
        'portal',
      );

      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should updateScheduleJob successfully with overriding regular jobs', async () => {
      const regularJob1Id = new ObjectId();
      const regularJob2Id = new ObjectId();

      await Promise.all([
        initMockJob({
          type: JobPeriodTypeEnum.PERIODIC,
          fIdentifier: 'PERIODIC_OVERRIDE_TEST',
          status: JobStatusEnum.OPEN,
        }),
        initMockJob({
          _id: regularJob1Id,
          type: JobPeriodTypeEnum.REGULAR,
          fIdentifier: 'PERIODIC_OVERRIDE_TEST',
          status: JobStatusEnum.OPEN,
          plannedDate: dayjs().add(1, 'day').toDate(),
        }),
        initMockJob({
          _id: regularJob2Id,
          type: JobPeriodTypeEnum.REGULAR,
          fIdentifier: 'PERIODIC_OVERRIDE_TEST',
          status: JobStatusEnum.OPEN,
          plannedDate: dayjs().add(2, 'days').toDate(),
        }),
        initMockJobPoint({ job: regularJob1Id }),
        initMockJobPoint({ job: regularJob2Id }),
        initMockJobEmployee({ job: regularJob1Id }),
        initMockJobEmployee({ job: regularJob2Id }),
      ]);

      const result = await service.updateScheduleJob(
        mockJobData._id.toString(),
        true,
        'portal',
      );

      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);

      const updatedJobPoints1 = await service['jobPointModel'].find({
        job: regularJob1Id,
      });
      const updatedJobPoints2 = await service['jobPointModel'].find({
        job: regularJob2Id,
      });
      const updatedEmployees1 = await service['jobEmployeeModel'].find({
        job: regularJob1Id,
      });
      const updatedEmployees2 = await service['jobEmployeeModel'].find({
        job: regularJob2Id,
      });

      expect(updatedJobPoints1.length).toBeGreaterThan(0);
      expect(updatedJobPoints2.length).toBeGreaterThan(0);
      expect(updatedEmployees1.length).toBeGreaterThan(0);
      expect(updatedEmployees2.length).toBeGreaterThan(0);
    });

    it('should updateScheduleJob with mobile platform', async () => {
      const result = await service.updateScheduleJob(
        mockJobData._id.toString(),
        false,
        'mobile',
      );

      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });
  });

  describe('generateScheduleJob', () => {
    it('should generate schedule job without fakeStartDate successfully', async () => {
      await initMockJob({ status: JobStatusEnum.OPEN } as any);
      const result = await service.generateScheduleJob();
      expect(result).toBeUndefined();
    });

    it('should generate schedule job with fakeStartDate successfully', async () => {
      const result = await service.generateScheduleJob({
        fakeStartDate: dayjs().subtract(90, 'day').toISOString(),
      });
      expect(result).toBeUndefined();
    });

    it('should return if do not have latest Regular Job', async () => {
      jest.spyOn(service['jobModel'], 'findOne').mockReturnValue({
        sort: jest.fn().mockReturnValue({
          lean: jest.fn().mockReturnValue(undefined),
        }),
      } as any);

      const result = await service.generateScheduleJob();
      expect(result).toBeUndefined();

      jest.restoreAllMocks();
    });
  });

  describe('updateScheduleJobSettings', () => {
    beforeEach(async () => {
      await initMockJob({
        type: JobPeriodTypeEnum.PERIODIC,
        status: JobStatusEnum.OPEN,
        fRule: JobFRuleEnum.DAILY,
        fInterval: 1,
        fStartDate: dayjs().add(1, 'day').toDate(),
        fEndDate: dayjs().add(1, 'month').toDate(),
      });
    });

    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();
      await expect(
        service.updateScheduleJobSettings({ id: jobId } as any),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND);
    });

    it.each([
      {
        ruleName: 'daily',
        rule: JobFRuleEnum.DAILY,
        interval: 2,
        endPeriod: { amount: 2, unit: 'months' as const },
        expectedFields: { fInterval: 2, fRule: JobFRuleEnum.DAILY },
        additionalFields: {},
      },
      {
        ruleName: 'weekly',
        rule: JobFRuleEnum.WEEKLY,
        interval: 1,
        endPeriod: { amount: 2, unit: 'months' as const },
        expectedFields: {
          fInterval: 1,
          fRule: JobFRuleEnum.WEEKLY,
          fDays: [JobFDaysEnum.MONDAY, JobFDaysEnum.WEDNESDAY],
        },
        additionalFields: {
          fDays: [JobFDaysEnum.MONDAY, JobFDaysEnum.WEDNESDAY],
        },
      },
      {
        ruleName: 'monthly',
        rule: JobFRuleEnum.MONTHLY,
        interval: 1,
        endPeriod: { amount: 6, unit: 'months' as const },
        expectedFields: {
          fInterval: 1,
          fRule: JobFRuleEnum.MONTHLY,
          fDayInMonth: 2,
        },
        additionalFields: { fDayInMonth: 2 },
      },
      {
        ruleName: 'yearly',
        rule: JobFRuleEnum.YEARLY,
        interval: 1,
        endPeriod: { amount: 2, unit: 'years' as const },
        expectedFields: { fInterval: 1, fRule: JobFRuleEnum.YEARLY },
        additionalFields: {},
      },
    ])(
      'should successfully update schedule job settings with $ruleName rule',
      async ({
        rule,
        interval,
        endPeriod,
        expectedFields,
        additionalFields,
      }) => {
        const updatePayload: UpdateScheduleJobSettingsDto = {
          id: mockJobData._id.toString(),
          fInterval: interval,
          fRule: rule,
          fStartDate: dayjs().add(1, 'day').toISOString(),
          fEndDate: dayjs().add(endPeriod.amount, endPeriod.unit).toISOString(),
          ...additionalFields,
        };

        const result = await service.updateScheduleJobSettings(updatePayload);

        expect(result).toBeDefined();
        Object.entries(expectedFields).forEach(([key, value]) => {
          expect(result[key]).toEqual(value);
        });
      },
    );

    it('should remove regular jobs when isRemoveRegular is true', async () => {
      const updatePayload: UpdateScheduleJobSettingsDto = {
        id: mockJobData._id.toString(),
        fInterval: 1,
        fRule: JobFRuleEnum.DAILY,
        fStartDate: dayjs().add(1, 'day').toISOString(),
        fEndDate: dayjs().add(1, 'month').toISOString(),
        isRemoveRegular: true,
      };

      const result = await service.updateScheduleJobSettings(updatePayload);
      expect(result).toBeDefined();

      const deletedJobs = await service['jobModel'].find({
        fIdentifier: mockJobData.fIdentifier,
        type: JobPeriodTypeEnum.REGULAR,
        isDeleted: true,
      });
      expect(deletedJobs.length).toBeGreaterThan(0);
    });

    it('should not remove regular jobs when isRemoveRegular is false', async () => {
      const updatePayload: UpdateScheduleJobSettingsDto = {
        id: mockJobData._id.toString(),
        fInterval: 1,
        fRule: JobFRuleEnum.DAILY,
        fStartDate: dayjs().add(1, 'day').toISOString(),
        fEndDate: dayjs().add(1, 'month').toISOString(),
        isRemoveRegular: false,
      };

      const result = await service.updateScheduleJobSettings(updatePayload);
      expect(result).toBeDefined();

      const activeJobs = await service['jobModel'].find({
        fIdentifier: mockJobData.fIdentifier,
        type: JobPeriodTypeEnum.REGULAR,
        isDeleted: { $ne: true },
      });
      expect(activeJobs.length).toBeGreaterThan(0);
    });

    it('should update job and generate new schedule when settings change', async () => {
      const updatePayload: UpdateScheduleJobSettingsDto = {
        id: mockJobData._id.toString(),
        fInterval: 3,
        fRule: JobFRuleEnum.WEEKLY,
        fStartDate: dayjs().add(2, 'days').toISOString(),
        fEndDate: dayjs().add(3, 'months').toISOString(),
        fDays: [JobFDaysEnum.MONDAY, JobFDaysEnum.FRIDAY],
      };

      const result = await service.updateScheduleJobSettings(updatePayload);
      expect(result).toBeDefined();
      expect(result.fInterval).toBe(3);
      expect(result.fRule).toBe(JobFRuleEnum.WEEKLY);
      expect(result.fDays).toEqual([JobFDaysEnum.MONDAY, JobFDaysEnum.FRIDAY]);

      const updatedJob = await service['jobModel'].findById(mockJobData._id);
      expect(updatedJob).toBeDefined();
      expect(updatedJob!.fInterval).toBe(3);
      expect(updatedJob!.fRule).toBe(JobFRuleEnum.WEEKLY);
    });
  });

  describe('viewSummaryJob', () => {
    beforeEach(async () => {
      await service['jobEquipmentModel'].deleteMany({});

      jest
        .spyOn(service['unitService'], 'findRootUnitOfLocation')
        .mockResolvedValue({
          _id: mockUnitData._id,
          name: mockUnitData.name,
          position: 0,
          isRoot: true,
        } as any);
    });

    it('should throw error if job not found', async () => {
      const jobId = new ObjectId();
      await expect(
        service.viewSummaryJob({ id: jobId.toString() } as any),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND);
    });

    const invalidStatusTestCases = [
      {
        description:
          'should throw error if job status not allow view in platform portal',
        platform: 'portal',
        status: JobStatusEnum.OPEN,
        user: mockTenantUserData._id.toString(),
      },
      {
        description:
          'should throw error if job status not allow view in platform portal with IN_PROGRESS status',
        platform: 'portal',
        status: JobStatusEnum.IN_PROGRESS,
        user: mockTenantUserData._id.toString(),
      },
      {
        description:
          'should throw error if job status not allow view in platform mobile with READY status',
        platform: 'mb',
        status: JobStatusEnum.READY,
        user: mockTenantUserData._id.toString(),
      },
      {
        description:
          'should throw error if job status not allow view in platform mobile with OPEN status',
        platform: 'mb',
        status: JobStatusEnum.OPEN,
        user: mockTenantUserData._id.toString(),
      },
    ];

    test.each(invalidStatusTestCases)(
      '$description',
      async ({ platform, status, user }) => {
        await initMockJob({ status });

        await expect(
          service.viewSummaryJob({
            id: mockJobData._id.toString(),
            platform,
            user,
          }),
        ).rejects.toThrow(JOB_KEYS.JOB_STATUS_NOT_ALLOW_VIEW_SUMMARY);
      },
    );

    it('should return empty employees array when user not in employees list for mobile platform', async () => {
      await initMockJob({ status: JobStatusEnum.IN_PROGRESS });

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        user: new ObjectId().toString(),
      };

      const result = await service.viewSummaryJob(payload);

      expect(result).toBeDefined();
      expect(result.employees).toEqual([]);
      expect(result).toMatchSchema(jobTest.viewSummaryJobSchema);
    });

    it('should return empty employees array when no employees found', async () => {
      await Promise.all([
        initMockJob({ status: JobStatusEnum.READY }),
        service['jobEmployeeModel'].deleteMany({}),
      ]);

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'portal',
        user: mockTenantUserData._id.toString(),
      };

      const result = await service.viewSummaryJob(payload);

      expect(result.employees).toEqual([]);
      expect(result).toMatchSchema(jobTest.viewSummaryJobSchema);
    });

    it('should return successfully view summary job', async () => {
      await service['jobPointModel'].deleteMany({});
      await Promise.all([
        initMockJob({ status: JobStatusEnum.READY }),
        initMockJobEmployee(),
        initMockJobPoint({
          _id: new ObjectId(),
          status: JobPointStatusEnum.GREEN,
          position: 1,
        }),
        initMockJobPoint({
          _id: new ObjectId(),
          status: JobPointStatusEnum.YELLOW,
          position: 2,
        }),
        initMockJobPoint({
          _id: new ObjectId(),
          status: JobPointStatusEnum.RED,
          position: 3,
        }),
      ]);

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'portal',
        user: mockTenantUserData._id.toString(),
      };

      const result = await service.viewSummaryJob(payload);
      expect(result).toBeDefined();
      expect(result.job._id).toEqual(mockJobData._id);
      expect(result.totalGreenResult).toBe(1);
      expect(result.totalYellowResult).toBe(1);
      expect(result.totalRedResult).toBe(1);
      expect(result).toMatchSchema(jobTest.viewSummaryJobSchema);
    });

    it('should return grouped and sorted units correctly', async () => {
      await Promise.all([
        initMockJob({ status: JobStatusEnum.READY }),
        initMockJobPoint({ status: JobPointStatusEnum.GREEN }),
        initMockJobEmployee(),
      ]);

      const payload = {
        id: mockJobData._id.toString(),
        platform: 'portal',
        user: mockTenantUserData._id.toString(),
      };

      const result = await service.viewSummaryJob(payload);
      expect(result.units).toBeDefined();
      expect(Array.isArray(result.units)).toBe(true);
      if (result.units.length > 0) {
        expect(result.units[0]).toHaveProperty('points');
        expect(Array.isArray(result.units[0].points)).toBe(true);
      }
      expect(result).toMatchSchema(jobTest.viewSummaryJobSchema);
    });
  });

  describe('checkOverlapJob', () => {
    const basePayload = {
      platform: 'portal',
      employees: [
        {
          employee: mockTenantUserData._id.toString(),
          plannedDate: dayjs().toISOString(),
        },
      ],
      equipments: [
        {
          equipment: mockEquipmentData._id.toString(),
          plannedDate: dayjs().toISOString(),
        },
      ],
      timezone: '+00:00',
      equipmentType: mockEquipmentData.type,
    };

    beforeEach(() => {
      jest
        .spyOn(service['equipmentService'], 'getAvailableEquipments')
        .mockResolvedValue([mockEquipmentData as any]);
    });

    it('should return empty employees and equipments when all available', async () => {
      // Employee works on the current day of week
      const currentDayOfWeek = dayjs().format('ddd').toLowerCase();
      const isCurrentWeekOdd = dayjs().isoWeek() % 2 === 1;

      await initMockTenantUser({
        oddWeeks: isCurrentWeekOdd
          ? [
              (currentDayOfWeek.charAt(0).toUpperCase() +
                currentDayOfWeek.slice(1)) as any,
            ]
          : [],
        evenWeeks: !isCurrentWeekOdd
          ? [
              (currentDayOfWeek.charAt(0).toUpperCase() +
                currentDayOfWeek.slice(1)) as any,
            ]
          : [],
      });

      const result = await service.checkOverlapJob(basePayload);
      expect(result.employees).toEqual([]);
      expect(result.equipments).toEqual([]);
      expect(result).toMatchSchema(jobTest.checkOverlapJobSchema);
    });

    it('should return overlap employees when employee not available on planned date', async () => {
      await initMockTenantUser({
        oddWeeks: [],
        evenWeeks: [],
      });

      const result = await service.checkOverlapJob(basePayload);
      expect(result.employees.length).toBe(1);
      expect(result.employees[0]).toHaveProperty('displayName');
      expect(result.employees[0]).toHaveProperty('oddWeeks', []);
      expect(result.employees[0]).toHaveProperty('evenWeeks', []);
      expect(result.equipments).toEqual([]);
      expect(result).toMatchSchema(jobTest.checkOverlapJobSchema);
    });

    describe('employee availability based on odd/even weeks', () => {
      const testCases = [
        {
          description:
            'should return overlap when employee works on odd weeks but planned date is in even week',
          oddWeeks: [
            WorkingDays.MONDAY,
            WorkingDays.WEDNESDAY,
            WorkingDays.FRIDAY,
          ],
          evenWeeks: [],
          plannedDate: '2024-01-08T00:00:00.000Z', // Monday of even week (week 2)
          expectedOverlap: true,
        },
        {
          description:
            'should return no overlap when employee works on odd weeks and planned date is in odd week',
          oddWeeks: [
            WorkingDays.MONDAY,
            WorkingDays.WEDNESDAY,
            WorkingDays.FRIDAY,
          ],
          evenWeeks: [],
          plannedDate: '2024-01-01T00:00:00.000Z', // Monday of odd week (week 1)
          expectedOverlap: false,
        },
        {
          description:
            'should return overlap when employee works on even weeks but planned date is in odd week',
          oddWeeks: [],
          evenWeeks: [WorkingDays.TUESDAY, WorkingDays.THURSDAY],
          plannedDate: '2024-01-02T00:00:00.000Z', // Tuesday of odd week (week 1)
          expectedOverlap: true,
        },
        {
          description:
            'should return no overlap when employee works on even weeks and planned date is in even week',
          oddWeeks: [],
          evenWeeks: [WorkingDays.TUESDAY, WorkingDays.THURSDAY],
          plannedDate: '2024-01-09T00:00:00.000Z', // Tuesday of even week (week 2)
          expectedOverlap: false,
        },
      ];

      test.each(testCases)(
        '$description',
        async ({ oddWeeks, evenWeeks, plannedDate, expectedOverlap }) => {
          await initMockTenantUser({
            oddWeeks,
            evenWeeks,
          });

          const payload = {
            ...basePayload,
            employees: [
              {
                employee: mockTenantUserData._id.toString(),
                plannedDate,
              },
            ],
          };

          const result = await service.checkOverlapJob(payload);
          if (expectedOverlap) {
            expect(result.employees.length).toBe(1);
          } else {
            expect(result.employees.length).toBe(0);
          }
          expect(result).toMatchSchema(jobTest.checkOverlapJobSchema);
        },
      );
    });
  });

  describe('coWorkerSyncDataOfJob', () => {
    it('should throw error if platform is not mobile', async () => {
      await expect(
        service.coWorkerSyncDataOfJob({
          platform: 'portal',
        } as any),
      ).rejects.toThrow(JOB_KEYS.PLATFORM_NOT_SUPPORT);
    });

    it('should throw error if job not found', async () => {
      const jobId = new ObjectId().toString();
      await expect(
        service.coWorkerSyncDataOfJob({
          id: jobId,
          platform: 'mb',
        } as any),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND);
    });

    it('should throw error if job status not allow sync data', async () => {
      await initMockJob({ status: JobStatusEnum.OPEN });

      await expect(
        service.coWorkerSyncDataOfJob({
          id: mockJobData._id.toString(),
          platform: 'mb',
        } as any),
      ).rejects.toThrow(JOB_KEYS.JOB_STATUS_NOT_ALLOW_SYNC_DATA);
    });

    it('should sync data with actualHours successfully', async () => {
      await initMockJob({ status: JobStatusEnum.IN_PROGRESS });
      const userId = new ObjectId();
      const plannedDateUTC = dayjs(mockJobData.plannedDate)
        .utc()
        .startOf('day')
        .toDate();
      await Promise.all([
        initMockTenantUser({
          _id: userId,
          username: faker.person.firstName(),
        }),
        initMockJobEmployee({
          employee: userId,
          plannedDate: plannedDateUTC,
        }),
      ]);

      const payload: any = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        actualHours: [
          {
            plannedDate: plannedDateUTC,
            hour: 120,
          },
        ],
        user: userId.toString(),
      };

      const result = await service.coWorkerSyncDataOfJob(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);

      // Verify job employee actualHours was updated
      const updatedJobEmployee = await service['jobEmployeeModel']
        .findOne({
          employee: userId,
          job: mockJobData._id,
          plannedDate: plannedDateUTC.toISOString(),
        })
        .lean();
      expect(updatedJobEmployee).toBeDefined();
      expect(updatedJobEmployee!.actualHours).toBe(120);
    });

    it('should sync data with feedbacks and fuaDescriptions', async () => {
      const userId = new ObjectId();
      await Promise.all([
        initMockTenantUser({
          _id: userId,
          username: faker.person.firstName(),
        }),
        initMockJobEmployee({ employee: userId }),
      ]);

      const payload: any = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        feedbacks: 'Test feedback from co-worker',
        fuaDescriptions: 'Test FUA description',
        user: userId.toString(),
      };

      const result = await service.coWorkerSyncDataOfJob(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);

      // Verify job was updated
      const updatedJob = await service['jobModel'].findById(mockJobData._id);
      expect(updatedJob).toBeDefined();
      expect(updatedJob!.feedbacks).toBe(payload.feedbacks);
      expect(updatedJob!.fuaDescriptions).toBe(payload.fuaDescriptions);
    });

    it('should sync data without actualHours', async () => {
      const userId = new ObjectId();
      await Promise.all([
        initMockTenantUser({
          _id: userId,
          username: faker.person.firstName(),
        }),
        initMockJobEmployee({ employee: userId }),
      ]);

      const payload: any = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        feedbacks: 'Feedback only',
        user: userId.toString(),
      };

      const result = await service.coWorkerSyncDataOfJob(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTest.findOneSchema);
    });

    it('should preserve existing data when not provided', async () => {
      const userId = new ObjectId();

      // Set initial data
      await Promise.all([
        initMockTenantUser({
          _id: userId,
          username: faker.person.firstName(),
        }),
        initMockJobEmployee({ employee: userId }),
        initMockJob({
          status: JobStatusEnum.IN_PROGRESS,
          feedbacks: 'Existing feedback',
          fuaDescriptions: 'Existing FUA',
        }),
      ]);

      const payload: any = {
        id: mockJobData._id.toString(),
        platform: 'mb',
        actualHours: [
          {
            plannedDate: mockJobData.plannedDate,
            actualHours: 1,
          },
        ],
        user: userId.toString(),
      };

      await service.coWorkerSyncDataOfJob(payload);

      // Verify existing data is preserved
      const updatedJob = await service['jobModel'].findById(mockJobData._id);
      expect(updatedJob).toBeDefined();
      expect(updatedJob!.feedbacks).toBe('Existing feedback');
      expect(updatedJob!.fuaDescriptions).toBe('Existing FUA');
    });
  });

  describe('getPdfReport', () => {
    let pdfClientSendSpy: jest.SpyInstance;
    const basePayload = { headers: {} };

    beforeEach(() => {
      pdfClientSendSpy = jest.fn().mockReturnValue(of({ data: {} }));
      (service as any).pdfClient = { send: pdfClientSendSpy };
    });

    const expectPendingResult = (shouldTriggerGeneration = false) => ({
      generatePdfStatus: GeneratePdfStatusEnum.PENDING,
      pdfPublicUrl: '',
      message: 'System still pending generate pdf report of job',
      shouldTriggerGeneration,
    });

    it('should throw error if job not found', async () => {
      await expect(
        service.getPdfReport({
          id: new ObjectId().toString(),
          ...basePayload,
        }),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND);
    });

    const pdfTestCases = [
      {
        description: 'should trigger PDF generation when status is NONE',
        jobData: {
          pdfPublicUrl: '',
          generatePdfStatus: GeneratePdfStatusEnum.NONE,
        },
        expected: expectPendingResult(true),
      },
      {
        description: 'should trigger PDF generation when no generatePdfStatus',
        jobData: { pdfPublicUrl: '', generatePdfStatus: '' as any },
        expected: expectPendingResult(true),
      },
      {
        description: 'should not trigger PDF generation when already PENDING',
        jobData: {
          pdfPublicUrl: '',
          generatePdfStatus: GeneratePdfStatusEnum.PENDING,
        },
        expected: expectPendingResult(false),
      },
      {
        description: 'should return PDF URL when generation is DONE',
        jobData: {
          pdfPublicUrl: 'https://example.com/report.pdf',
          generatePdfStatus: GeneratePdfStatusEnum.DONE,
        },
        expected: {
          generatePdfStatus: GeneratePdfStatusEnum.DONE,
          pdfPublicUrl: 'https://example.com/report.pdf',
          shouldTriggerGeneration: false,
        },
      },
      {
        description: 'should return existing URL when status is FAILED',
        jobData: {
          pdfPublicUrl: 'https://example.com/failed.pdf',
          generatePdfStatus: GeneratePdfStatusEnum.FAILED,
        },
        expected: {
          generatePdfStatus: GeneratePdfStatusEnum.FAILED,
          pdfPublicUrl: 'https://example.com/failed.pdf',
          shouldTriggerGeneration: false,
        },
      },
    ];

    test.each(pdfTestCases)('$description', async ({ jobData, expected }) => {
      await initMockJob(jobData);

      const result = await service.getPdfReport({
        id: mockJobData._id.toString(),
        ...basePayload,
      });

      const { shouldTriggerGeneration, ...expectedResult } = expected;
      expect(result).toEqual(expectedResult);

      if (shouldTriggerGeneration) {
        expect(pdfClientSendSpy).toHaveBeenCalledWith(
          { cmd: JOB_MESSAGES.GET_PDF_REPORT_OF_JOB },
          { id: mockJobData._id.toString(), ...basePayload },
        );
      } else {
        expect(pdfClientSendSpy).not.toHaveBeenCalled();
      }
    });
  });

  describe('deleteScheduleJob', () => {
    const basePayload = {
      platform: 'portal',
      user: mockTenantUserData._id.toString(),
    };

    beforeEach(() => {
      jest
        .spyOn(service as any, 'validatePermissionOfUser')
        .mockReturnValue(true);
    });

    const expectSuccessfulDeletion = (result: any) => {
      expect(result).toBeDefined();
      expect(result.acknowledged).toBe(true);
      expect(result.modifiedCount).toBeGreaterThan(0);
    };

    it('should throw error platform not support for platform mb', async () => {
      await expect(
        service.deleteScheduleJob({ platform: 'mb' } as any),
      ).rejects.toThrow(JOB_KEYS.PLATFORM_NOT_SUPPORT);
    });

    it('should throw error if job not found', async () => {
      await expect(
        service.deleteScheduleJob({
          jobId: new ObjectId().toString(),
          ...basePayload,
        } as any),
      ).rejects.toThrow(JOB_KEYS.JOBS_NOT_FOUND);
    });

    const deleteTestCases = [
      {
        description: 'should delete schedule job and keep all regular jobs',
        isKeepAllRegularJobs: true,
        fIdentifier: 'SCHEDULE_DELETE_KEEP',
        setupRegularJobs: false,
        expectedRegularJobsDeleted: 0,
      },
      {
        description:
          'should delete schedule job and remove all related regular jobs',
        isKeepAllRegularJobs: false,
        fIdentifier: 'SCHEDULE_DELETE_REMOVE',
        setupRegularJobs: true,
        expectedRegularJobsDeleted: 2,
      },
      {
        description: 'should delete schedule job when no regular jobs exist',
        isKeepAllRegularJobs: false,
        fIdentifier: 'SCHEDULE_DELETE_NO_REGULAR',
        setupRegularJobs: false,
        expectedRegularJobsDeleted: 0,
      },
    ];

    test.each(deleteTestCases)(
      '$description',
      async ({
        isKeepAllRegularJobs,
        fIdentifier,
        setupRegularJobs,
        expectedRegularJobsDeleted,
      }) => {
        const regularJobIds: any[] = [];

        const setupPromises = [
          initMockJob({
            type: JobPeriodTypeEnum.PERIODIC,
            fIdentifier,
            status: JobStatusEnum.OPEN,
          }),
        ];

        if (setupRegularJobs) {
          const regularJob1Id = new ObjectId();
          const regularJob2Id = new ObjectId();
          regularJobIds.push(regularJob1Id, regularJob2Id);

          setupPromises.push(
            initMockJob({
              _id: regularJob1Id,
              type: JobPeriodTypeEnum.REGULAR,
              fIdentifier,
              status: JobStatusEnum.OPEN,
              plannedDate: dayjs().add(1, 'day').toDate(),
            }),
            initMockJob({
              _id: regularJob2Id,
              type: JobPeriodTypeEnum.REGULAR,
              fIdentifier,
              status: JobStatusEnum.OPEN,
              plannedDate: dayjs().add(2, 'days').toDate(),
            }),
          );
        }

        await Promise.all(setupPromises);

        const result = await service.deleteScheduleJob({
          jobId: mockJobData._id.toString(),
          isKeepAllRegularJobs,
          ...basePayload,
        });

        expectSuccessfulDeletion(result);

        if (expectedRegularJobsDeleted > 0) {
          const deletedRegularJobs = await service['jobModel']
            .find({
              _id: { $in: regularJobIds },
              isDeleted: true,
            })
            .lean();
          expect(deletedRegularJobs).toHaveLength(expectedRegularJobsDeleted);
        }

        // Always verify the periodic job is deleted
        const deletedPeriodicJob = await service['jobModel']
          .findOne({ _id: mockJobData._id, isDeleted: true })
          .lean();
        expect(deletedPeriodicJob).toBeDefined();
      },
    );
  });

  describe('detailsPlanningJob', () => {
    beforeAll(async () => {
      await service['jobModel'].deleteMany({});
    });

    it('should return null if job not found', async () => {
      const jobId = new ObjectId().toString();

      const result = await service.detailsPlanningJob(jobId);
      expect(result).toBeUndefined();
    });

    it('should return job details without scheduler when no periodic job exists', async () => {
      await initMockJob({
        type: JobPeriodTypeEnum.REGULAR,
        status: JobStatusEnum.OPEN,
        fIdentifier: 'REGULAR_JOB_001',
      });

      const result = await service.detailsPlanningJob(
        mockJobData._id.toString(),
      );

      expect(result).toBeDefined();
      expect(result._id).toEqual(mockJobData._id);
      expect(result.identifier).toBe(mockJobData.identifier);
      expect(result.title).toBe(mockJobData.title);
      expect(result.status).toBe(JobStatusQueryEnum.OPEN);
      expect(result.fIdentifier).toBe('REGULAR_JOB_001');
      expect(result.scheduler).toBeUndefined();
      expect(result.employees).toBeDefined();
      expect(Array.isArray(result.employees)).toBe(true);
      expect(result).toMatchSchema(jobTest.detailsPlanningJobSchema);
    });

    it('should return job details with scheduler when periodic job exists', async () => {
      const periodicJobId = new ObjectId();
      const fIdentifier = 'SCHEDULE_PLANNING_001';

      await Promise.all([
        initMockJob({
          _id: periodicJobId,
          type: JobPeriodTypeEnum.PERIODIC,
          fIdentifier: fIdentifier,
          status: JobStatusEnum.OPEN,
        }),
        initMockJob({
          type: JobPeriodTypeEnum.REGULAR,
          fIdentifier: fIdentifier,
          status: JobStatusEnum.OPEN,
        }),
        initMockJobEmployee(),
      ]);

      const result = await service.detailsPlanningJob(
        mockJobData._id.toString(),
      );

      expect(result).toBeDefined();
      expect(result._id).toEqual(mockJobData._id);
      expect(result.identifier).toBe(mockJobData.identifier);
      expect(result.status).toBe(JobStatusQueryEnum.OPEN);
      expect(result.fIdentifier).toBe(fIdentifier);
      expect(result.scheduler).toEqual(periodicJobId);
      expect(result.employees).toBeDefined();
      expect(Array.isArray(result.employees)).toBe(true);
      expect(result.employees.length).toBeGreaterThan(0);
      expect(result.employees[0]).toEqual(mockTenantUserData._id);
      expect(result).toMatchSchema(jobTest.detailsPlanningJobSchema);
    });
  });

  describe('updatePlanningJob', () => {
    it('should throw error if plannedDate is in the past', async () => {
      const payload: any = {
        plannedDate: dayjs().subtract(1, 'day').toISOString(),
      };

      await expect(service.updatePlanningJob(payload)).rejects.toThrow(
        JOB_KEYS.PLANNED_DATE_MUST_BE_GREATER_OR_EQUAL_THAN_NOW,
      );
    });

    it('should throw error if job not found', async () => {
      const payload: any = {
        id: new ObjectId().toString(),
        plannedDate: dayjs().add(1, 'day').toISOString(),
      };

      await expect(service.updatePlanningJob(payload)).rejects.toThrow(
        JOB_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if job status is not OPEN or IN_PROGRESS', async () => {
      await initMockJob({
        status: JobStatusEnum.COMPLETE,
      });

      const payload: any = {
        id: mockJobData._id.toString(),
        plannedDate: dayjs().add(1, 'day').toISOString(),
      };

      await expect(service.updatePlanningJob(payload)).rejects.toThrow(
        JOB_KEYS.JOB_STATUS_NOT_ALLOW_UPDATE,
      );
    });

    it('should update plannedDate successfully', async () => {
      await initMockJob({
        status: JobStatusEnum.OPEN,
      });

      const payload: any = {
        id: mockJobData._id.toString(),
        plannedDate: dayjs().add(1, 'day').toISOString(),
      };

      const result = await service.updatePlanningJob(payload);
      expect(result).toBeDefined();
      expect(result._id).toEqual(mockJobData._id);
      expect(dayjs(result.plannedDate)).toEqual(dayjs(payload.plannedDate));
      expect(result).toMatchSchema(jobTest.detailsPlanningJobSchema);
    });
  });

  describe('getEmployeesSchedules', () => {
    const period = {
      startDate: dayjs().startOf('day').toDate(),
      endDate: dayjs().add(7, 'days').endOf('day').toDate(),
      timezone: '+00:00',
    };

    it('should return empty array when no jobs found for employees', async () => {
      const nonExistentEmployeeIds = [new ObjectId(), new ObjectId()];

      const result = await service.getEmployeesSchedules(
        period,
        nonExistentEmployeeIds,
      );

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(0);
    });

    it('should return employees schedules successfully', async () => {
      await initMockJob({
        type: JobPeriodTypeEnum.REGULAR,
        status: JobStatusEnum.OPEN,
        plannedDate: dayjs().add(1, 'day').toDate(),
      });

      const result = await service.getEmployeesSchedules(period, [
        mockTenantUserData._id,
      ]);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);

      if (result.length > 0) {
        expect(result[0]).toHaveProperty('employee');
        expect(result[0]).toHaveProperty('schedules');
        expect(result[0].employee).toHaveProperty('_id');
        expect(result[0].employee).toHaveProperty('displayName');
        expect(Array.isArray(result[0].schedules)).toBe(true);
      }
    });

    it('should filter by location when provided', async () => {
      const result = await service.getEmployeesSchedules(
        period,
        [mockTenantUserData._id],
        mockLocationData._id.toString(),
      );

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('getEquipmentSchedules', () => {
    const period = {
      startDate: dayjs().startOf('day').toDate(),
      endDate: dayjs().add(7, 'days').endOf('day').toDate(),
      timezone: '+00:00',
    };

    it('should return empty array when no jobs found for equipment type', async () => {
      const result = await service.getEquipmentSchedules(
        period,
        EquipmentEnum.DEVICE,
      );

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(0);
    });

    it('should return equipment schedules successfully for CAR type', async () => {
      await initMockJob({
        type: JobPeriodTypeEnum.REGULAR,
        status: JobStatusEnum.OPEN,
        plannedDate: dayjs().add(1, 'day').toDate(),
        equipments: [mockEquipmentData._id],
      });

      const result = await service.getEquipmentSchedules(
        period,
        EquipmentEnum.CAR,
      );

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);

      if (result.length > 0) {
        expect(result[0]).toHaveProperty('equipment');
        expect(result[0]).toHaveProperty('schedules');
        expect(result[0].equipment).toHaveProperty('_id');
        expect(result[0].equipment).toHaveProperty('description');
        expect(Array.isArray(result[0].schedules)).toBe(true);
      }
    });

    it('should filter by location when provided', async () => {
      const result = await service.getEquipmentSchedules(
        period,
        EquipmentEnum.CAR,
        mockLocationData._id.toString(),
      );

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('updateActualHoursForEmployees', () => {
    it('should update actualHours for employees successfully', async () => {
      const plannedDate = dayjs().utc().startOf('day').toDate();
      const actualHours = [
        {
          hour: 2,
          plannedDate: plannedDate,
        },
      ];

      await initMockJobEmployee({
        job: mockJobData._id,
        employee: mockTenantUserData._id,
        plannedDate: plannedDate,
      });

      await service['updateActualHoursForEmployees'](
        actualHours,
        mockJobData._id.toString(),
        mockTenantUserData._id.toString(),
      );

      const updatedJobEmployee = await service['jobEmployeeModel'].findOne({
        employee: mockTenantUserData._id,
        job: mockJobData._id,
        plannedDate: plannedDate,
      });
      expect(updatedJobEmployee).toBeDefined();
      expect(updatedJobEmployee!.actualHours).toBe(2);
    });
  });

  describe('processActualHoursUpdate', () => {
    it('should update actualHours success for platform mb', async () => {
      const plannedDate = dayjs().utc().startOf('day').toDate();
      const actualHours = [
        {
          hour: 5,
          plannedDate: plannedDate,
        },
      ];

      await initMockJobEmployee({
        job: mockJobData._id,
        employee: mockTenantUserData._id,
        plannedDate: plannedDate,
      });

      await service['processActualHoursUpdate'](
        'mb',
        actualHours,
        mockJobData._id.toString(),
        mockTenantUserData._id.toString(),
      );

      const updatedJobEmployee = await service['jobEmployeeModel'].findOne({
        employee: mockTenantUserData._id,
        job: mockJobData._id,
        plannedDate: plannedDate,
      });
      expect(updatedJobEmployee).toBeDefined();
      expect(updatedJobEmployee!.actualHours).toBe(5);
    });

    it('should not update actualHours for platform portal', async () => {
      await expect(
        service['processActualHoursUpdate'](
          'portal',
          [
            {
              hour: 5,
              plannedDate: dayjs().toDate(),
            },
          ],
          mockJobData._id.toString(),
          mockTenantUserData._id.toString(),
        ),
      ).resolves.toBeUndefined();
    });
  });

  describe('processAssigneeChange', () => {
    let sendEmailSpy: jest.SpyInstance;

    beforeEach(() => {
      sendEmailSpy = jest
        .spyOn(service as any, 'sendEmailWhenAssigneeJob')
        .mockResolvedValue(undefined);
    });

    afterEach(() => {
      sendEmailSpy.mockRestore();
    });

    const testCases = [
      {
        description: 'should not send email when assignee is not changed',
        currentAssignee: mockTenantUserData._id,
        newAssignee: mockTenantUserData._id,
        shouldSendEmail: false,
      },
      {
        description: 'should not send email when newAssignee is null',
        currentAssignee: mockTenantUserData._id,
        newAssignee: null,
        shouldSendEmail: false,
      },
      {
        description: 'should not send email when newAssignee is undefined',
        currentAssignee: mockTenantUserData._id,
        newAssignee: undefined,
        shouldSendEmail: false,
      },
      {
        description: 'should send email when assignee is changed',
        currentAssignee: mockTenantUserData._id,
        newAssignee: new ObjectId(),
        shouldSendEmail: true,
      },
    ];

    test.each(testCases)(
      '$description',
      async ({ currentAssignee, newAssignee, shouldSendEmail }) => {
        const jobId = mockJobData._id.toString();
        const tenantId = mockTenantData._id.toString();

        await (service as any).processAssigneeChange(
          currentAssignee,
          newAssignee,
          jobId,
          tenantId,
        );

        if (shouldSendEmail) {
          expect(sendEmailSpy).toHaveBeenCalledWith(jobId, tenantId);
          expect(sendEmailSpy).toHaveBeenCalledTimes(1);
        } else {
          expect(sendEmailSpy).not.toHaveBeenCalled();
        }
      },
    );
  });

  describe('handleReservedUpdates', () => {
    let inventoryClientSendSpy: jest.SpyInstance;

    beforeEach(() => {
      inventoryClientSendSpy = jest
        .spyOn(service['inventoryClient'], 'send')
        .mockReturnValue(of({ data: {} }));
      inventoryClientSendSpy.mockClear();
    });

    afterEach(() => {
      inventoryClientSendSpy.mockRestore();
    });

    it('should return early when reserved is not provided', async () => {
      await (service as any).handleReservedUpdates(null, '', '');
      expect(inventoryClientSendSpy).not.toHaveBeenCalled();
    });

    it('should call inventoryClient with UPDATE_RESERVED when reserved has id', async () => {
      const reserved = {
        _id: new ObjectId().toString(),
        storage: mockStorageData._id.toString(),
        reservedDetails: [{ article: 'test-article', amount: 10 }],
      };
      const jobId = mockJobData._id.toString();
      const userId = mockTenantUserData._id.toString();

      await (service as any).handleReservedUpdates(reserved, jobId, userId);

      expect(inventoryClientSendSpy).toHaveBeenCalledWith(
        { cmd: RESERVED_MESSAGES.UPDATE_RESERVED },
        {
          body: reserved,
          user: userId,
        },
      );
    });

    it('should call inventoryClient with CREATE_RESERVED when reserved has no id', async () => {
      const reserved = {
        storage: mockStorageData._id.toString(),
        reservedDetails: [{ article: 'test-article', amount: 5 }],
      };
      const jobId = mockJobData._id.toString();
      const userId = mockTenantUserData._id.toString();

      await (service as any).handleReservedUpdates(reserved, jobId, userId);

      expect(inventoryClientSendSpy).toHaveBeenCalledWith(
        { cmd: RESERVED_MESSAGES.CREATE_RESERVED },
        {
          body: {
            ...reserved,
            job: jobId,
          },
          user: userId,
        },
      );
    });

    it('should throw BadRequestException when update reservation fails', async () => {
      const errorResponse = {
        error: {
          message: 'Validation failed',
          errors: ['Storage not found'],
        },
      };

      inventoryClientSendSpy.mockReturnValue(throwError(() => errorResponse));

      const reserved = {
        _id: new ObjectId().toString(),
        storage: new ObjectId().toString(),
        reservedDetails: [{ article: 'test-article', amount: 10 }],
      };
      const jobId = mockJobData._id.toString();
      const userId = mockTenantUserData._id.toString();

      await expect(
        (service as any).handleReservedUpdates(reserved, jobId, userId),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('validateJobUnits', () => {
    it('should return empty array if units is not provided', async () => {
      const result = await (service as any).validateJobUnits(
        mockJobData,
        null,
        'portal',
      );
      expect(result).toBeDefined();
      expect(result.updatedUnitIds).toHaveLength(0);
    });
  });

  describe('deleteOldImages', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should not call service delete if oldImages is undefined', async () => {
      const httpDeleteSpy = jest.spyOn(service['httpService'], 'delete');

      await (service as any).deleteOldImages(undefined, ['image1.png'], {});
      expect(httpDeleteSpy).not.toHaveBeenCalled();
    });

    it('should not call service delete if newImages is undefined', async () => {
      const httpDeleteSpy = jest.spyOn(service['httpService'], 'delete');

      await (service as any).deleteOldImages(['image1.png'], undefined, {});
      expect(httpDeleteSpy).not.toHaveBeenCalled();
    });

    it('should not call service delete if no difference between images', async () => {
      const httpDeleteSpy = jest.spyOn(service['httpService'], 'delete');
      const images = ['image1.png', 'image2.png'];

      await (service as any).deleteOldImages(images, images, {});
      expect(httpDeleteSpy).not.toHaveBeenCalled();
    });

    it('should call service delete for deleted images', async () => {
      const mockUploadFile = {
        _id: new ObjectId(),
        publicUrl: 'deleted-image.png',
      };

      await initMockUploadFile(mockUploadFile);

      const httpDeleteSpy = jest
        .spyOn(service['httpService'], 'delete')
        .mockReturnValue(of({} as any));

      const oldImages = ['image1.png', 'deleted-image.png'];
      const newImages = ['image1.png'];

      await (service as any).deleteOldImages(oldImages, newImages, {});
      expect(httpDeleteSpy).toHaveBeenCalled();
    });
  });

  describe('validateEquipments', () => {
    it('should return empty if equipments is not provided', async () => {
      await expect(
        (service as any).validateEquipments(null),
      ).resolves.toBeUndefined();
    });

    it('should throw error if equipments not found', async () => {
      await expect(
        (service as any).validateEquipments([new ObjectId().toString()]),
      ).rejects.toThrow(JOB_KEYS.EQUIPMENTS_NOT_FOUND);
    });
  });

  describe('getValidReportContact', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should handle CUSTOMER report type and return contact._id', async () => {
      jest
        .spyOn(service['contactService'], 'getValidContact')
        .mockResolvedValue(mockContactData);

      const result = await (service as any).getValidReportContact(
        JobReportTypeEnum.CUSTOMER,
        mockContactData._id.toString(),
      );

      expect(result).toBe(mockContactData._id);
    });

    it('should handle SUPPLIER report type and return contact._id', async () => {
      jest
        .spyOn(service['contactService'], 'getValidContact')
        .mockResolvedValue(mockContactData);

      const result = await (service as any).getValidReportContact(
        JobReportTypeEnum.SUPPLIER,
        mockContactData._id.toString(),
      );

      expect(result).toBe(mockContactData._id);
    });

    it('should handle INTERNAL report type and return contact._id', async () => {
      jest.restoreAllMocks();
      await initMockContact({ isInternal: true });

      const result = await (service as any).getValidReportContact(
        JobReportTypeEnum.INTERNAL,
        null,
      );
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id');
    });

    it('should throw error when contact not found', async () => {
      jest
        .spyOn(service['contactService'], 'getValidContact')
        .mockResolvedValue(null);

      await expect(
        (service as any).getValidReportContact(
          JobReportTypeEnum.CUSTOMER,
          'invalid-id',
        ),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND_CONTACT);
    });

    it('should throw error when internal contact not found', async () => {
      await service['contactModel'].deleteMany({ isInternal: true });

      await expect(
        (service as any).getValidReportContact(
          JobReportTypeEnum.INTERNAL,
          null,
        ),
      ).rejects.toThrow(JOB_KEYS.NOT_FOUND_CONTACT);
    });
  });

  describe('validateEmployees', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return early if no assignee', async () => {
      const validateRoleOfEmployeesSpy = jest.spyOn(
        service as any,
        'validateRoleOfEmployees',
      );

      await (service as any).validateEmployees(
        null,
        [mockTenantUserData._id.toString()],
        JobTypeEnum.INSPECTION,
      );
      expect(validateRoleOfEmployeesSpy).not.toHaveBeenCalled();
    });

    it('should return early if no employees', async () => {
      const validateRoleOfEmployeesSpy = jest.spyOn(
        service as any,
        'validateRoleOfEmployees',
      );

      await (service as any).validateEmployees(
        mockTenantUserData._id.toString(),
        [],
        JobTypeEnum.INSPECTION,
      );
      expect(validateRoleOfEmployeesSpy).not.toHaveBeenCalled();
    });

    it('should throw error if assignee not in employee list', async () => {
      const assigneeId = new ObjectId().toString();
      const employeeId = mockTenantUserData._id.toString();

      await expect(
        (service as any).validateEmployees(
          assigneeId,
          [employeeId],
          JobTypeEnum.INSPECTION,
        ),
      ).rejects.toThrow(JOB_KEYS.EMPLOYEE_MUST_HAVE_ASSIGNEE);
    });

    test.each([
      [JobTypeEnum.INSPECTION, TenantRoleEnum.INSPECTOR],
      [JobTypeEnum.MAINTENANCE, TenantRoleEnum.MECHANIC],
      [JobTypeEnum.CLEANING, TenantRoleEnum.CLEANER],
    ])(
      'should validate %s job type with %s role',
      async (jobType, expectedRole) => {
        const validateRoleOfEmployeesSpy = jest
          .spyOn(service as any, 'validateRoleOfEmployees')
          .mockResolvedValue(undefined);

        await (service as any).validateEmployees(
          mockTenantUserData._id.toString(),
          [mockTenantUserData._id.toString()],
          jobType,
        );
        expect(validateRoleOfEmployeesSpy).toHaveBeenCalledWith(
          [mockTenantUserData._id.toString()],
          expectedRole,
        );
      },
    );
  });

  describe('validateReviewer', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    test.each([
      [
        JobTypeEnum.INSPECTION,
        [TenantRoleEnum.INSPECTION_REVIEWER_ALL_INSPECTIONS],
      ],
      [JobTypeEnum.MAINTENANCE, [TenantRoleEnum.MAINTENANCE_REVIEWER]],
      [JobTypeEnum.CLEANING, [TenantRoleEnum.CLEANING_REVIEWER]],
    ])(
      'should validate %s job type with %s role',
      async (jobType, expectedRoles) => {
        const validateRoleOfReviewerSpy = jest
          .spyOn(service as any, 'validateRoleOfReviewer')
          .mockResolvedValue(undefined);

        await (service as any).validateReviewer(
          mockTenantUserData._id.toString(),
          jobType,
        );

        expect(validateRoleOfReviewerSpy).toHaveBeenCalledWith(
          mockTenantUserData._id.toString(),
          expectedRoles,
        );
      },
    );
  });

  describe('validatePlanner', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return early if no planner', async () => {
      const validateRoleOfPlannerSpy = jest.spyOn(
        service as any,
        'validateRoleOfPlanner',
      );

      await (service as any).validatePlanner(null, JobTypeEnum.INSPECTION);

      expect(validateRoleOfPlannerSpy).not.toHaveBeenCalled();
    });

    test.each([
      [
        JobTypeEnum.INSPECTION,
        [TenantRoleEnum.INSPECTION_PLANNER, TenantRoleEnum.INSPECTOR],
      ],
      [
        JobTypeEnum.MAINTENANCE,
        [TenantRoleEnum.MAINTENANCE_PLANNER, TenantRoleEnum.MECHANIC],
      ],
      [
        JobTypeEnum.CLEANING,
        [TenantRoleEnum.CLEANING_PLANNER, TenantRoleEnum.CLEANER],
      ],
    ])(
      'should validate %s job type with %s roles',
      async (jobType, expectedRoles) => {
        const validateRoleOfPlannerSpy = jest
          .spyOn(service as any, 'validateRoleOfPlanner')
          .mockResolvedValue(undefined);

        await (service as any).validatePlanner(
          mockTenantUserData._id.toString(),
          jobType,
        );

        expect(validateRoleOfPlannerSpy).toHaveBeenCalledWith(
          mockTenantUserData._id.toString(),
          expectedRoles,
        );
      },
    );
  });

  describe('validateRoleOfUser', () => {
    it('should return true if user has role', async () => {
      jest
        .spyOn(service['tenantUserService'], 'verifyRoles')
        .mockResolvedValue(true);

      const result = await (service as any).validateRoleOfUser(
        mockTenantUserData._id.toString(),
        [TenantRoleEnum.INSPECTION_PLANNER],
      );

      expect(result).toBe(true);
    });

    it('should return false if user does not have role', async () => {
      jest
        .spyOn(service['tenantUserService'], 'verifyRoles')
        .mockResolvedValue(false);

      const result = await (service as any).validateRoleOfUser(
        mockTenantUserData._id.toString(),
        [TenantRoleEnum.INSPECTION_PLANNER],
      );

      expect(result).toBe(false);
    });
  });

  describe('getJobTypesWhenGetAllJobForMobile', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should not remove job type if user has valid role', async () => {
      jest.spyOn(service as any, 'validateRoleOfUser').mockResolvedValue(true);

      const jobTypes = [JobTypeEnum.INSPECTION, JobTypeEnum.MAINTENANCE];

      await (service as any).getJobTypesWhenGetAllJobForMobile(
        jobTypes,
        mockTenantUserData._id.toString(),
        TenantRoleEnum.INSPECTOR,
        JobTypeEnum.INSPECTION,
      );

      expect(jobTypes).toHaveLength(2);
      expect(jobTypes).toContain(JobTypeEnum.INSPECTION);
    });

    it('should remove job type if user does not have valid role', async () => {
      jest.spyOn(service as any, 'validateRoleOfUser').mockResolvedValue(false);

      const jobTypes = [JobTypeEnum.INSPECTION, JobTypeEnum.MAINTENANCE];

      await (service as any).getJobTypesWhenGetAllJobForMobile(
        jobTypes,
        mockTenantUserData._id.toString(),
        TenantRoleEnum.INSPECTOR,
        JobTypeEnum.INSPECTION,
      );

      expect(jobTypes).toHaveLength(1);
      expect(jobTypes).not.toContain(JobTypeEnum.INSPECTION);
      expect(jobTypes).toContain(JobTypeEnum.MAINTENANCE);
    });
  });

  describe('updateJobPoints', () => {
    it('should update existing job points when job status is OPEN and platform is portal', async () => {
      await initMockJobPoint();

      const mockJob = {
        _id: mockJobData._id,
        status: JobStatusEnum.OPEN,
      };

      const mockUnitsPayload = [
        {
          _id: mockUnitData._id.toString(),
          points: [
            {
              _id: mockJobPointData._id.toString(),
              description: 'Updated point description',
              position: 1,
            },
          ],
        },
      ];

      await (service as any).updateJobPoints(
        mockUnitsPayload,
        mockJob,
        'portal',
      );

      const updatedJobPoint = await service['jobPointModel'].findById(
        mockJobPointData._id,
      );
      expect(updatedJobPoint).toBeTruthy();
      expect(updatedJobPoint!.description).toBe('Updated point description');
      expect(updatedJobPoint!.position).toBe(1);
    });

    it('should update job points when job status is IN_PROGRESS and platform is mobile', async () => {
      await initMockJobPoint({
        status: JobPointStatusEnum.GREEN,
        notes: 'Original notes',
        images: ['original-image.jpg'],
      });

      const mockJob = {
        _id: mockJobData._id,
        status: JobStatusEnum.IN_PROGRESS,
      };

      const mockUnitsPayload = [
        {
          _id: mockUnitData._id.toString(),
          points: [
            {
              _id: mockJobPointData._id.toString(),
              status: JobPointStatusEnum.YELLOW,
              notes: 'Updated notes for in progress',
              images: ['updated-image.jpg'],
            },
          ],
        },
      ];

      await (service as any).updateJobPoints(mockUnitsPayload, mockJob, 'mb');

      const updatedJobPoint = await service['jobPointModel'].findById(
        mockJobPointData._id,
      );
      expect(updatedJobPoint).toBeTruthy();
      expect(updatedJobPoint!.status).toBe(JobPointStatusEnum.YELLOW);
      expect(updatedJobPoint!.notes).toBe('Updated notes for in progress');
      expect(updatedJobPoint!.images).toEqual(['updated-image.jpg']);
    });
  });

  describe('bulkWriteJobPoints', () => {
    it('should throw error when YELLOW status point missing notes', async () => {
      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.YELLOW,
              notes: '',
              images: ['image1.jpg'],
            },
          ],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(unitsPayload, {
          _id: mockJobData._id,
        }),
      ).rejects.toThrow(JOB_KEYS.JOB_POINTS_MISS_NOTES_OR_IMAGES);
    });

    it('should throw error when RED status point missing images', async () => {
      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.RED,
              notes: 'Some notes',
              images: [],
            },
          ],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(unitsPayload, {
          _id: mockJobData._id,
        }),
      ).rejects.toThrow(JOB_KEYS.JOB_POINTS_MISS_NOTES_OR_IMAGES);
    });

    it('should throw error when creating cost line for INTERNAL report type job on mobile', async () => {
      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.GREEN,
              notes: 'Some notes',
              images: ['image1.jpg'],
              costLines: [
                {
                  description: 'New cost line',
                  price: 100,
                  quantity: 1,
                  position: 1,
                },
              ],
            },
          ],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(
          unitsPayload,
          { _id: mockJobData._id, reportType: JobReportTypeEnum.INTERNAL },
          'mb',
        ),
      ).rejects.toThrow(
        JOB_KEYS.CANNOT_CREATE_COSTLINE_FOR_JOB_HAVE_REPORT_TYPE_INTERNAL,
      );
    });

    it('should throw error when cost type is invalid on portal', async () => {
      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.GREEN,
              notes: 'Some notes',
              images: ['image1.jpg'],
              costLines: [
                {
                  _id: mockCostlineData._id,
                  description: 'Cost line',
                  price: 100,
                  quantity: 1,
                  position: 1,
                  costType: new ObjectId(),
                },
              ],
            },
          ],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(
          unitsPayload,
          { _id: mockJobData._id },
          'portal',
        ),
      ).rejects.toThrow(JOB_KEYS.INVALID_COST_TYPE);
    });

    it('should throw error when total job points count mismatch', async () => {
      await initMockJobPoint({
        _id: new ObjectId(),
        job: mockJobData._id,
        unit: mockUnitData._id,
      });

      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.GREEN,
              notes: 'Some notes',
              images: ['image1.jpg'],
            },
          ],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(unitsPayload, {
          _id: mockJobData._id,
        }),
      ).rejects.toThrow(JOB_KEYS.MISS_SOME_JOB_POINTS_OR_JOB_POINTS_NOT_FOUND);
    });

    it('should throw error when job points per unit count mismatch', async () => {
      const extraUnit = new ObjectId();
      const extraJobPoint = new ObjectId();

      await initMockUnit({ _id: extraUnit });
      await initMockJobPoint({
        _id: extraJobPoint,
        job: mockJobData._id,
        unit: extraUnit,
      });

      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.GREEN,
              notes: 'Some notes',
              images: ['image1.jpg'],
            },
          ],
        },
        {
          _id: extraUnit,
          points: [],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(unitsPayload, {
          _id: mockJobData._id,
        }),
      ).rejects.toThrow(JOB_KEYS.MISS_SOME_JOB_POINTS_OR_JOB_POINTS_NOT_FOUND);
    });

    it('should throw error when unit has more job points in payload than in DB', async () => {
      await service['jobPointModel'].deleteMany({});
      const jobPointId = new ObjectId();
      await Promise.all([
        initMockJobPoint(),
        initMockJobPoint({ _id: jobPointId, unit: new ObjectId() }),
      ]);

      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.GREEN,
              notes: 'Some notes',
              images: ['image1.jpg'],
            },
            {
              _id: jobPointId,
              status: JobPointStatusEnum.GREEN,
              notes: 'Some notes',
              images: ['image1.jpg'],
            },
          ],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(unitsPayload, {
          _id: mockJobData._id,
        }),
      ).rejects.toThrow(JOB_KEYS.MISS_SOME_JOB_POINTS_OR_JOB_POINTS_NOT_FOUND);
    });

    it('should successfully update job points with GREEN status', async () => {
      await service['jobPointModel'].deleteMany({});

      await Promise.all([
        await initMockJob(),
        await initMockUnit(),
        await initMockJobPoint(),
      ]);

      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.GREEN,
              notes: 'Updated notes',
              images: ['updated-image.jpg'],
            },
          ],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(unitsPayload, {
          _id: mockJobData._id,
        }),
      ).resolves.not.toThrow();

      const updatedJobPoint = await service['jobPointModel'].findById(
        mockJobPointData._id,
      );
      expect(updatedJobPoint!.status).toBe(JobPointStatusEnum.GREEN);
      expect(updatedJobPoint!.notes).toBe('Updated notes');
      expect(updatedJobPoint!.images).toEqual(['updated-image.jpg']);
    });

    it('should successfully update job points with cost lines', async () => {
      await initMockCostType({ type: CostTypeType.JOB_OR_CUSTOM });

      const unitsPayload = [
        {
          _id: mockUnitData._id,
          points: [
            {
              _id: mockJobPointData._id,
              status: JobPointStatusEnum.YELLOW,
              notes: 'Notes for yellow status',
              images: ['image1.jpg'],
              costLines: [
                {
                  description: 'New cost line',
                  price: 150,
                  quantity: 2,
                  position: 1,
                  costType: mockCostTypeData._id,
                },
              ],
            },
          ],
        },
      ];

      await expect(
        (service as any).bulkWriteJobPoints(
          unitsPayload,
          { _id: mockJobData._id },
          'portal',
        ),
      ).resolves.not.toThrow();

      const updatedJobPoint = await service['jobPointModel'].findById(
        mockJobPointData._id,
      );
      expect(updatedJobPoint!.status).toBe(JobPointStatusEnum.YELLOW);
      expect(updatedJobPoint!.notes).toBe('Notes for yellow status');
    });
  });

  describe('updateCostLineOfJobPoints', () => {
    let mockJob: any;
    let mockUnit: any;
    let mockCostLine1: any;
    let mockCostLine2: any;

    beforeEach(async () => {
      mockJob = { _id: new ObjectId(), location: new ObjectId() };
      mockUnit = new ObjectId();
      mockCostLine1 = new ObjectId();
      mockCostLine2 = new ObjectId();

      await initMockCostline({ _id: mockCostLine1 });
      await initMockCostline({ _id: mockCostLine2 });
    });

    describe('Error cases', () => {
      it('should throw error when cost line to update is not in job point', async () => {
        const costLines = [
          {
            _id: mockCostLine1,
            description: 'Update cost line',
            price: 100,
            quantity: 1,
            position: 1,
          },
        ];
        const costLineOfPointIds = [mockCostLine2.toString()]; // Khác với cost line trong payload

        await expect(
          (service as any).updateCostLineOfJobPoints(
            costLines,
            costLineOfPointIds,
            mockUnit,
            mockJob,
          ),
        ).rejects.toThrow(JOB_KEYS.COSTLINE_ARE_NOT_IN_JOB_POINT);
      });

      it('should throw error when position validation fails', async () => {
        const costLines = [
          {
            description: 'New cost line 1',
            price: 100,
            quantity: 1,
            position: 2, // Position không bắt đầu từ 1
          },
          {
            description: 'New cost line 2',
            price: 200,
            quantity: 1,
            position: 3,
          },
        ];

        await expect(
          (service as any).updateCostLineOfJobPoints(
            costLines,
            [],
            mockUnit,
            mockJob,
          ),
        ).rejects.toThrow('job.form.position_must_start_by_1');
      });
    });

    describe('Success cases', () => {
      it('should create new cost lines successfully', async () => {
        const costLines = [
          {
            description: 'New cost line 1',
            price: 100,
            quantity: 2,
            position: 1,
            costType: mockCostTypeData._id,
          },
          {
            description: 'New cost line 2',
            price: 200,
            quantity: 1,
            position: 2,
            costType: mockCostTypeData._id,
          },
        ];

        const result = await (service as any).updateCostLineOfJobPoints(
          costLines,
          [],
          mockUnit,
          mockJob,
        );

        expect(result.insertedIds).toBeDefined();
        expect(Object.keys(result.insertedIds)).toHaveLength(2);
        expect(result.updatedIds).toEqual([]);

        // Kiểm tra cost lines đã được tạo trong DB
        const createdCostLines = await service['costLineModel'].find({
          job: mockJob._id,
        });
        expect(createdCostLines).toHaveLength(2);
        expect(createdCostLines[0].totalPrice).toBe(200); // 100 * 2
        expect(createdCostLines[1].totalPrice).toBe(200); // 200 * 1
      });

      it('should update existing cost lines successfully', async () => {
        const costLines = [
          {
            _id: mockCostLine1.toString(), // Convert to string
            description: 'Updated cost line',
            price: 150,
            quantity: 3,
            position: 1,
            costType: mockCostTypeData._id,
          },
        ];
        const costLineOfPointIds = [mockCostLine1.toString()];

        const result = await (service as any).updateCostLineOfJobPoints(
          costLines,
          costLineOfPointIds,
          mockUnit,
          mockJob,
        );

        expect(result.insertedIds).toEqual({});
        expect(result.updatedIds).toEqual([mockCostLine1.toString()]);

        // Kiểm tra cost line đã được update
        const updatedCostLine =
          await service['costLineModel'].findById(mockCostLine1);
        expect(updatedCostLine!.description).toBe('Updated cost line');
        expect(updatedCostLine!.totalPrice).toBe(450); // 150 * 3
      });

      it('should delete cost lines not in payload', async () => {
        const costLines = [
          {
            _id: mockCostLine1.toString(),
            description: 'Keep this cost line',
            price: 100,
            quantity: 1,
            position: 1,
            costType: mockCostTypeData._id,
          },
        ];
        const costLineOfPointIds = [
          mockCostLine1.toString(),
          mockCostLine2.toString(),
        ];

        await (service as any).updateCostLineOfJobPoints(
          costLines,
          costLineOfPointIds,
          mockUnit,
          mockJob,
        );

        // Kiểm tra cost line 2 đã được đánh dấu xóa
        const deletedCostLine =
          await service['costLineModel'].findById(mockCostLine2);
        expect(deletedCostLine!.isDeleted).toBe(true);

        // Kiểm tra cost line 1 vẫn còn
        const keptCostLine =
          await service['costLineModel'].findById(mockCostLine1);
        expect(keptCostLine!.isDeleted).toBe(false);
      });

      it('should handle mixed operations (create, update, delete)', async () => {
        const costLines = [
          {
            _id: mockCostLine1,
            description: 'Updated existing',
            price: 100,
            quantity: 1,
            position: 1,
            costType: mockCostTypeData._id,
          },
          {
            description: 'New cost line',
            price: 200,
            quantity: 2,
            position: 2,
            costType: mockCostTypeData._id,
          },
        ];
        const costLineOfPointIds = [
          mockCostLine1.toString(),
          mockCostLine2.toString(),
        ];

        const result = await (service as any).updateCostLineOfJobPoints(
          costLines,
          costLineOfPointIds,
          mockUnit,
          mockJob,
        );

        // Kiểm tra kết quả
        expect(Object.keys(result.insertedIds)).toHaveLength(1); // 1 created
        expect(result.updatedIds).toEqual([mockCostLine1]); // 1 updated

        // Kiểm tra cost line 1 đã được update
        const updatedCostLine =
          await service['costLineModel'].findById(mockCostLine1);
        expect(updatedCostLine!.description).toBe('Updated existing');

        // Kiểm tra cost line 2 đã được đánh dấu xóa
        const deletedCostLine =
          await service['costLineModel'].findById(mockCostLine2);
        expect(deletedCostLine!.isDeleted).toBe(true);

        // Kiểm tra cost line mới đã được tạo
        const newCostLines = await service['costLineModel'].find({
          job: mockJob._id,
          _id: { $nin: [mockCostLine1, mockCostLine2] },
        });
        expect(newCostLines).toHaveLength(1);
        expect(newCostLines[0].description).toBe('New cost line');
        expect(newCostLines[0].totalPrice).toBe(400); // 200 * 2
      });

      it('should handle empty cost lines array', async () => {
        const costLineOfPointIds = [
          mockCostLine1.toString(),
          mockCostLine2.toString(),
        ];

        const result = await (service as any).updateCostLineOfJobPoints(
          [],
          costLineOfPointIds,
          mockUnit,
          mockJob,
        );

        expect(result.insertedIds).toEqual({});
        expect(result.updatedIds).toEqual([]);

        // Kiểm tra tất cả cost lines đã được đánh dấu xóa
        const deletedCostLine1 =
          await service['costLineModel'].findById(mockCostLine1);
        const deletedCostLine2 =
          await service['costLineModel'].findById(mockCostLine2);
        expect(deletedCostLine1!.isDeleted).toBe(true);
        expect(deletedCostLine2!.isDeleted).toBe(true);
      });
    });
  });
});
