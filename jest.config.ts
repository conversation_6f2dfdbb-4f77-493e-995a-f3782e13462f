import type { Config } from 'jest';
import { basename, dirname } from 'path';

const jsonReportPath =
  process.env.JEST_CTRF_JSON_OUTPUT_FILE ?? 'dist/jest_reporter/report.json';
const junitReportPath =
  process.env.JEST_JUNIT_OUTPUT_FILE ?? 'dist/jest_reporter/report.xml';

const config: Config = {
  rootDir: 'src',
  moduleFileExtensions: ['js', 'json', 'ts'],
  moduleNameMapper: {
    '^~$': ['<rootDir>/'],
    '^~/(.*)$': ['<rootDir>/$1'],
  },
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['**/*.(t|j)s'],
  coveragePathIgnorePatterns: [
    // Folders
    '.*/test/.*',
    '.*/scripts/.*',
    '.*/common/.*',
    '.*/configs/.*',
    '.*/constants/.*',
    '.*/transformers/.*',
    '.*/utils/.*',
    '.*/processors/.*',
    // Files
    'main.ts',
    '\\.dto\\.ts$',
    '\\.module\\.ts$',
    '\\.controller\\.ts$',
    '\\.model\\.ts$',
    '\\.pipe\\.ts$',
  ],
  coverageDirectory: '../dist/coverage',
  // collectCoverage: true, // Enable coverage collection
  coverageReporters: ['json', 'lcov', 'text', 'clover'],
  testEnvironment: 'node',
  globalSetup: '<rootDir>/test/global-setup.ts',
  globalTeardown: '<rootDir>/test/global-teardown.ts',
  setupFilesAfterEnv: ['<rootDir>/test/setup.ts'],
  testTimeout: 300000,
  maxWorkers: 1,
  forceExit: true,
  verbose: true,
  silent: true,
  reporters: [
    'default',
    [
      'jest-ctrf-json-reporter',
      {
        outputFile: basename(jsonReportPath),
        outputDir: dirname(jsonReportPath),
      },
    ],
    [
      'jest-junit',
      {
        outputDirectory: dirname(junitReportPath),
        outputName: basename(junitReportPath),
      },
    ],
    [
      'jest-html-reporters',
      {
        publicPath: './dist/jest_reporter',
        filename: 'report.html',
        pageTitle: 'EEAC-Core Report',
        openReport: false,
        inlineSource: true,
        darkTheme: true,
        expand: true,
      },
    ],
  ],
};

export default config;
